'use client';

import type { ReactNode } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// --- NEW: A simple CSS string for the animation overlay ---
const overlayStyles = `
  .scroll-animation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none; /* Allows clicks to pass through */
    z-index: 100;
    visibility: hidden;
    transform: translateY(0);
    overflow: hidden; /* Ensures cloned content doesn't spill */
  }
  .scroll-animation-overlay.is-animating-down {
    visibility: visible;
    transition: transform 0.8s cubic-bezier(0.76, 0, 0.24, 1), visibility 0s 0.8s;
    transform: translateY(100%);
  }
`;

const PageWrapper = ({ children }: { children: ReactNode }) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  // --- NEW: Ref for our animation overlay ---
  const overlayRef = useRef<HTMLDivElement>(null);

  // --- REVISED LOCKING MECHANISM: DYNAMIC COOLDOWN ---
  const isAnimating = useRef(false);
  const lastActionTime = useRef(0);
  const MOUSE_KEYBOARD_COOLDOWN = 600;
  const TOUCHPAD_COOLDOWN = 1200;
  const SLIDE_ANIMATION_DURATION = 800; // Must match CSS transition-duration

  // --- TALL COMPONENT HANDLING ---
  const isSectionTallerThanViewport = useCallback(
    (sectionIndex: number): boolean => {
      const section = sectionsRef.current[sectionIndex];
      if (!section) return false;
      return section.scrollHeight > window.innerHeight + 5; // Add small buffer
    },
    [],
  );

  const getSectionScrollProgress = useCallback(
    (sectionIndex: number): number => {
      const section = sectionsRef.current[sectionIndex];
      if (!section) return 0;

      const sectionTop = section.offsetTop;
      const sectionHeight = section.scrollHeight;
      const viewportHeight = window.innerHeight;
      const currentScroll = window.pageYOffset;

      const scrolledIntoSection = currentScroll - sectionTop;
      const maxScrollInSection = sectionHeight - viewportHeight;

      if (maxScrollInSection <= 0) return 1;

      return Math.max(0, Math.min(1, scrolledIntoSection / maxScrollInSection));
    },
    [],
  );

  const isAtBottomOfSection = useCallback(
    (sectionIndex: number): boolean => {
      const progress = getSectionScrollProgress(sectionIndex);
      return progress >= 0.95; // Use a tolerance
    },
    [getSectionScrollProgress],
  );

  useEffect(() => {
    // --- NEW: Inject the overlay styles into the head ---
    const styleTag = document.createElement('style');
    styleTag.innerHTML = overlayStyles;
    document.head.appendChild(styleTag);

    const handleResize = () => setIsMobile(window.innerWidth < 768);
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (document.head.contains(styleTag)) {
        document.head.removeChild(styleTag);
      }
    };
  }, []);

  useEffect(() => {
    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b;
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b;
    };

    const performScroll = (direction: number) => {
      if (isAnimating.current) return;

      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (window.pageYOffset >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      const isCurrentSectionTall =
        isSectionTallerThanViewport(currentSectionIndex);

      if (isCurrentSectionTall) {
        if (
          (direction > 0 && !isAtBottomOfSection(currentSectionIndex)) ||
          (direction < 0 &&
            window.pageYOffset >
              sectionsRef.current[currentSectionIndex].offsetTop + 50)
        ) {
          window.scrollBy({
            top: window.innerHeight * 0.8 * direction,
            behavior: 'smooth',
          });
          return;
        }
      }

      const nextSectionIndex = currentSectionIndex + direction;

      if (
        nextSectionIndex < 0 ||
        nextSectionIndex >= sectionsRef.current.length
      ) {
        return; // Do nothing if at the very top or bottom
      }

      // --- MODIFIED: CHECK FOR CUSTOM EXIT ANIMATION ---
      const currentChild = React.Children.toArray(children)[
        currentSectionIndex
      ] as React.ReactElement;
      const exitAnimation = currentChild?.props.exitAnimation;

      // If scrolling down and the current section has a slide-down exit...
      if (direction > 0 && exitAnimation === 'slide-down') {
        isAnimating.current = true;
        const currentSectionNode = sectionsRef.current[currentSectionIndex];
        const targetSection = sectionsRef.current[nextSectionIndex];
        const targetPosition = targetSection.offsetTop;

        if (overlayRef.current && currentSectionNode) {
          // 1. Clone the current section and put it in the overlay
          const clonedNode = currentSectionNode.cloneNode(
            true,
          ) as HTMLDivElement;
          overlayRef.current.innerHTML = ''; // Clear previous clone
          overlayRef.current.appendChild(clonedNode);

          // 2. Instantly scroll the page to the target (hidden under the overlay)
          window.scrollTo(0, targetPosition);
          setActiveSection(nextSectionIndex);

          // 3. Animate the overlay away by adding the CSS class
          overlayRef.current.classList.add('is-animating-down');
        }

        // 4. Clean up after the animation
        setTimeout(() => {
          if (overlayRef.current) {
            overlayRef.current.classList.remove('is-animating-down');
            overlayRef.current.innerHTML = ''; // Clean up DOM
          }
          isAnimating.current = false;
        }, SLIDE_ANIMATION_DURATION);
      } else {
        // --- ORIGINAL SCROLL ANIMATION ---
        isAnimating.current = true;
        const targetSection = sectionsRef.current[nextSectionIndex];
        const targetPosition = targetSection.offsetTop;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const duration = 400; // A bit faster for normal scroll
        let startTime: number | null = null;

        const animateScroll = (time: number) => {
          if (startTime === null) startTime = time;
          const timeElapsed = time - startTime;
          const run = ease(timeElapsed, startPosition, distance, duration);
          window.scrollTo(0, run);
          if (timeElapsed < duration) {
            animationFrameId.current = requestAnimationFrame(animateScroll);
          } else {
            window.scrollTo(0, targetPosition);
            animationFrameId.current = null;
            setActiveSection(nextSectionIndex);
            isAnimating.current = false;
          }
        };
        animationFrameId.current = requestAnimationFrame(animateScroll);
      }
    };

    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();

      const currentTime = new Date().getTime();
      const isTouchpad = event.deltaY % 1 !== 0 || Math.abs(event.deltaY) < 50;
      const cooldown = isTouchpad ? TOUCHPAD_COOLDOWN : MOUSE_KEYBOARD_COOLDOWN;

      if (currentTime - lastActionTime.current < cooldown) return;
      if (isAnimating.current) return;
      if (Math.abs(event.deltaY) < 10) return;

      lastActionTime.current = currentTime;
      const direction = event.deltaY > 0 ? 1 : -1;
      performScroll(direction);
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      const navKeys = ['ArrowUp', 'ArrowDown', 'PageUp', 'PageDown', 'Space'];
      if (navKeys.includes(event.code)) event.preventDefault();

      const currentTime = new Date().getTime();
      if (currentTime - lastActionTime.current < MOUSE_KEYBOARD_COOLDOWN)
        return;
      if (isAnimating.current) return;

      let direction = 0;
      switch (event.code) {
        case 'ArrowDown':
        case 'PageDown':
        case 'Space':
          direction = 1;
          break;
        case 'ArrowUp':
        case 'PageUp':
          direction = -1;
          break;
        default:
          return;
      }

      if (direction !== 0) {
        performScroll(direction);
        lastActionTime.current = currentTime;
      }
    };

    const handleMobileScroll = () => {
      const currentScrollY = window.pageYOffset;
      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (currentScrollY >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }
      if (currentSectionIndex !== activeSection) {
        setActiveSection(currentSectionIndex);
      }
    };

    if (!isMobile) {
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('keydown', handleKeyDown);
    } else {
      window.addEventListener('scroll', handleMobileScroll, { passive: true });
    }

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('scroll', handleMobileScroll);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [
    isMobile,
    activeSection,
    children, // Added children as a dependency
    isSectionTallerThanViewport,
    isAtBottomOfSection,
    getSectionScrollProgress,
  ]);

  return (
    <>
      {/* --- NEW: The animation overlay div, rendered outside the main flow --- */}
      <div ref={overlayRef} className="scroll-animation-overlay"></div>

      <div>
        {React.Children.map(children, (child, index) => (
          <div
            ref={(el) => {
              if (el) sectionsRef.current[index] = el;
            }}
          >
            {React.cloneElement(child as React.ReactElement, {
              isActive: index === activeSection,
              isMobile,
            })}
          </div>
        ))}
      </div>
    </>
  );
};

export default PageWrapper;
