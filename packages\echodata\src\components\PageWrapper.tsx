'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

// CSS styles for scroll direction animations
const scrollAnimationStyles = `
  .scroll-animation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: 9999;
    visibility: hidden;
    transform: translateY(0);
    overflow: hidden;
    background: rgba(255, 0, 0, 0.1);
  }
  .scroll-animation-overlay.is-animating-down {
    visibility: visible;
    transition: transform 0.8s cubic-bezier(0.76, 0, 0.24, 1), visibility 0s 0s;
    transform: translateY(100%);
  }
  .scroll-animation-overlay.is-animating-up {
    visibility: visible;
    transition: transform 0.8s cubic-bezier(0.76, 0, 0.24, 1), visibility 0s 0s;
    transform: translateY(-100%);
  }
`;

interface PageWrapperProps {
  children: React.ReactNode;
  sectionConfigs?: Array<{
    scrollDirection?: 'up' | 'down' | 'default';
    scrollSkip?: boolean;
    scrollHalf?: boolean;
  }>;
}

const PageWrapper = ({ children, sectionConfigs = [] }: PageWrapperProps) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  // --- REVISED LOCKING MECHANISM: DYNAMIC COOLDOWN ---
  const isAnimating = useRef(false);
  const lastActionTime = useRef(0);
  const MOUSE_KEYBOARD_COOLDOWN = 600; // A shorter, responsive cooldown
  const TOUCHPAD_COOLDOWN = 1200; // A longer cooldown to absorb touchpad momentum
  const SCROLL_ANIMATION_DURATION = 800; // Animation duration for custom scroll directions

  useEffect(() => {
    // Inject scroll animation styles
    const styleTag = document.createElement('style');
    styleTag.id = 'page-wrapper-scroll-animations';
    styleTag.innerHTML = scrollAnimationStyles;

    // Remove any existing style tag first
    const existingStyle = document.getElementById(
      'page-wrapper-scroll-animations',
    );
    if (existingStyle) {
      existingStyle.remove();
    }

    document.head.appendChild(styleTag);

    const handleResize = () => setIsMobile(window.innerWidth < 768);
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (document.head.contains(styleTag)) {
        document.head.removeChild(styleTag);
      }
    };
  }, []);

  // --- Your original helper functions ---
  const isSectionTallerThanViewport = useCallback(
    (sectionIndex: number): boolean => {
      const section = sectionsRef.current[sectionIndex];
      if (!section) return false;
      return section.scrollHeight > window.innerHeight + 5; // Add small buffer
    },
    [],
  );

  const getSectionScrollProgress = useCallback(
    (sectionIndex: number): number => {
      const section = sectionsRef.current[sectionIndex];
      if (!section) return 0;

      const sectionTop = section.offsetTop;
      const sectionHeight = section.scrollHeight;
      const viewportHeight = window.innerHeight;
      const currentScroll = window.pageYOffset;

      const scrolledIntoSection = currentScroll - sectionTop;
      const maxScrollInSection = sectionHeight - viewportHeight;

      if (maxScrollInSection <= 0) return 1;

      return Math.max(0, Math.min(1, scrolledIntoSection / maxScrollInSection));
    },
    [],
  );

  const isAtBottomOfSection = useCallback(
    (sectionIndex: number): boolean => {
      const progress = getSectionScrollProgress(sectionIndex);
      return progress >= 0.95; // Use a tolerance
    },
    [getSectionScrollProgress],
  );

  useEffect(() => {
    const performScroll = (direction: number) => {
      if (isAnimating.current) return;

      let currentSectionIndex = 0;

      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (window.pageYOffset >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      // Check if current section has custom scroll direction
      // Use sectionConfigs if provided, otherwise try to detect from children
      const sectionConfig = sectionConfigs[currentSectionIndex];
      const scrollDirection = sectionConfig?.scrollDirection || 'default';
      const skipScrolling = sectionConfig?.scrollSkip || false;
      const halfScrolling = sectionConfig?.scrollHalf || false;

      const isCurrentSectionTall =
        isSectionTallerThanViewport(currentSectionIndex);
      if (isCurrentSectionTall) {
        const section = sectionsRef.current[currentSectionIndex];
        const sectionTop = section.offsetTop;
        const sectionBottom = sectionTop + section.scrollHeight;
        const viewportHeight = window.innerHeight;

        const isAtSectionTop = window.pageYOffset <= sectionTop + 5;
        const isAtSectionBottom =
          window.pageYOffset >= sectionBottom - viewportHeight - 5;

        const snapTo = (targetPosition: number) => {
          isAnimating.current = true;
          const startPosition = window.pageYOffset;
          const distance = targetPosition - startPosition;
          if (Math.abs(distance) < 5) {
            isAnimating.current = false;
            return;
          }
          const duration = 400;
          let startTime: number | null = null;

          const animateScroll = (time: number) => {
            if (startTime === null) startTime = time;
            const timeElapsed = time - startTime;
            const run = ease(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) {
              animationFrameId.current = requestAnimationFrame(animateScroll);
            } else {
              window.scrollTo(0, targetPosition);
              animationFrameId.current = null;
              isAnimating.current = false;
            }
          };
          animationFrameId.current = requestAnimationFrame(animateScroll);
        };

        if (direction > 0 && !isAtSectionBottom) {
          snapTo(sectionBottom - viewportHeight);
          return;
        }
        if (direction < 0 && !isAtSectionTop) {
          snapTo(sectionTop);
          return;
        }
      }

      if (
        (skipScrolling && direction > 0) ||
        (skipScrolling && direction < 0)
      ) {
        window.scrollBy({
          top: window.innerHeight * 2 * direction,
          behavior: 'smooth',
        });
        return;
      }

      const nextSectionIndex = currentSectionIndex + direction;

      if (skipScrolling) {
        let nextSectionIndex = currentSectionIndex;

        do {
          nextSectionIndex += direction;
        } while (
          nextSectionIndex >= 0 &&
          nextSectionIndex < sectionsRef.current.length &&
          sectionConfigs[nextSectionIndex]?.scrollSkip
        );

        // If we’re outside bounds, stop
        if (
          nextSectionIndex < 0 ||
          nextSectionIndex >= sectionsRef.current.length
        )
          return;
      }

      if (
        nextSectionIndex < 0 ||
        nextSectionIndex >= sectionsRef.current.length
      ) {
        const targetPosition =
          nextSectionIndex < 0 ? 0 : document.documentElement.scrollHeight;
        const newActiveSection =
          nextSectionIndex < 0 ? 0 : sectionsRef.current.length - 1;
        if (activeSection !== newActiveSection)
          setActiveSection(newActiveSection);
        window.scrollTo({ top: targetPosition, behavior: 'smooth' });
        return;
      }

      // Handle custom scroll directions
      if (direction > 0 && scrollDirection === 'down') {
        // Check if this is the FeatureTwo section (blue background expansion)
        const isFeatureTwoSection = currentSectionIndex === 2; // FeatureTwo is at index 2

        if (isFeatureTwoSection) {
          // Special animation for FeatureTwo: hide content and expand blue background
          isAnimating.current = true;
          const currentSectionNode = sectionsRef.current[currentSectionIndex];
          const targetSection = sectionsRef.current[nextSectionIndex];
          const targetPosition = targetSection.offsetTop;

          if (overlayRef.current && currentSectionNode && targetSection) {
            // Create overlay with blue background that expands
            const blueExpandOverlay = document.createElement('div');
            blueExpandOverlay.style.position = 'absolute';
            blueExpandOverlay.style.top = '0';
            blueExpandOverlay.style.left = '0';
            blueExpandOverlay.style.width = '100%';
            blueExpandOverlay.style.height = '100vh';
            blueExpandOverlay.style.background = '#047AFF';
            blueExpandOverlay.style.zIndex = '1';
            blueExpandOverlay.style.transition = 'all 0.8s cubic-bezier(0.76, 0, 0.24, 1)';

            // Clone the next section (GetStarted)
            const nextSectionClone = targetSection.cloneNode(true) as HTMLDivElement;
            const removeIds = (element: Element) => {
              element.removeAttribute('id');
              Array.from(element.children).forEach(removeIds);
            };
            removeIds(nextSectionClone);

            nextSectionClone.style.position = 'absolute';
            nextSectionClone.style.top = '100vh';
            nextSectionClone.style.left = '0';
            nextSectionClone.style.width = '100%';
            nextSectionClone.style.minHeight = '100vh';
            nextSectionClone.style.zIndex = '2';
            nextSectionClone.style.transition = 'top 0.8s cubic-bezier(0.76, 0, 0.24, 1)';

            // Add both to overlay
            overlayRef.current.innerHTML = '';
            overlayRef.current.appendChild(blueExpandOverlay);
            overlayRef.current.appendChild(nextSectionClone);

            // Show overlay
            overlayRef.current.style.visibility = 'visible';

            // Immediately scroll to target position
            window.scrollTo(0, targetPosition);
            setActiveSection(nextSectionIndex);

            // Start animations
            setTimeout(() => {
              if (overlayRef.current) {
                // Blue background expands and slides down
                blueExpandOverlay.style.transform = 'translateY(100vh)';

                // Next section slides up
                nextSectionClone.style.top = '0';
              }
            }, 50);
          }
        } else {
          // Default slide-down animation for other sections
          isAnimating.current = true;
          const currentSectionNode = sectionsRef.current[currentSectionIndex];
          const targetSection = sectionsRef.current[nextSectionIndex];
          const targetPosition = targetSection.offsetTop;

          if (overlayRef.current && currentSectionNode && targetSection) {
            // Create two overlays: one for the current section (behind) and one for the next section (in front)
            const currentSectionClone = currentSectionNode.cloneNode(
              true,
            ) as HTMLDivElement;
            const nextSectionClone = targetSection.cloneNode(
              true,
            ) as HTMLDivElement;

            // Remove IDs to avoid conflicts
            const removeIds = (element: Element) => {
              element.removeAttribute('id');
              Array.from(element.children).forEach(removeIds);
            };
            removeIds(currentSectionClone);
            removeIds(nextSectionClone);

            // Style the current section clone (slides down, behind)
            currentSectionClone.style.position = 'absolute';
            currentSectionClone.style.top = '0';
            currentSectionClone.style.left = '0';
            currentSectionClone.style.width = '100%';
            currentSectionClone.style.minHeight = '100vh';
            currentSectionClone.style.zIndex = '1';

            // Style the next section clone (slides up, in front)
            nextSectionClone.style.position = 'absolute';
            nextSectionClone.style.top = '100vh'; // Start below viewport
            nextSectionClone.style.left = '0';
            nextSectionClone.style.width = '100%';
            nextSectionClone.style.minHeight = '100vh';
            nextSectionClone.style.zIndex = '2';
            nextSectionClone.style.transition =
              'top 0.8s cubic-bezier(0.76, 0, 0.24, 1)';

            // Add both clones to overlay
            overlayRef.current.innerHTML = '';
            overlayRef.current.appendChild(currentSectionClone);
            overlayRef.current.appendChild(nextSectionClone);

            // Show overlay BEFORE scrolling to prevent seeing the background
            overlayRef.current.style.visibility = 'visible';

            // Immediately scroll to target position (hidden behind overlay)
            window.scrollTo(0, targetPosition);
            setActiveSection(nextSectionIndex);

            // Start animations after a small delay
            setTimeout(() => {
              if (overlayRef.current) {
                // Current section slides down (behind)
                currentSectionClone.style.transition =
                  'top 0.8s cubic-bezier(0.76, 0, 0.24, 1)';
                currentSectionClone.style.top = '100vh';

                // Next section slides up (in front)
                nextSectionClone.style.top = '0';
              }
            }, 50);
          }
        }

        // Clean up after animation
        setTimeout(() => {
          if (overlayRef.current) {
            overlayRef.current.style.visibility = 'hidden';
            overlayRef.current.innerHTML = '';
          }
          isAnimating.current = false;
        }, SCROLL_ANIMATION_DURATION);

        // Return early to prevent normal scroll animation
        return;
      } else if (direction < 0 && scrollDirection === 'up') {
        // Handle upward custom scroll direction
        isAnimating.current = true;
        const currentSectionNode = sectionsRef.current[currentSectionIndex];
        const targetSection = sectionsRef.current[nextSectionIndex];
        const targetPosition = targetSection.offsetTop;

        if (overlayRef.current && currentSectionNode) {
          // Clone current section and put it in overlay
          const clonedNode = currentSectionNode.cloneNode(
            true,
          ) as HTMLDivElement;
          overlayRef.current.innerHTML = '';
          overlayRef.current.appendChild(clonedNode);

          // Instantly scroll to target position (hidden under overlay)
          window.scrollTo(0, targetPosition);
          setActiveSection(nextSectionIndex);

          // Animate overlay up
          overlayRef.current.classList.add('is-animating-up');
        }

        // Clean up after animation
        setTimeout(() => {
          if (overlayRef.current) {
            overlayRef.current.classList.remove('is-animating-up');
            overlayRef.current.innerHTML = '';
          }
          isAnimating.current = false;
        }, SCROLL_ANIMATION_DURATION);
      } else {
        // Default scroll behavior
        isAnimating.current = true;
        const targetSection = sectionsRef.current[nextSectionIndex];
        const targetPosition = targetSection.offsetTop;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const duration = 200;
        let startTime: number | null = null;

        const animateScroll = (time: number) => {
          if (startTime === null) startTime = time;
          const timeElapsed = time - startTime;
          const run = ease(timeElapsed, startPosition, distance, duration);
          window.scrollTo(0, run);
          if (timeElapsed < duration) {
            animationFrameId.current = requestAnimationFrame(animateScroll);
          } else {
            window.scrollTo(0, targetPosition);
            animationFrameId.current = null;
            setActiveSection(nextSectionIndex);
            isAnimating.current = false;
          }
        };
        animationFrameId.current = requestAnimationFrame(animateScroll);
      }
    };

    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();

      const currentTime = new Date().getTime();

      // Detect touchpad vs mouse wheel and select the appropriate cooldown
      const isTouchpad = event.deltaY % 1 !== 0 || Math.abs(event.deltaY) < 50;
      const cooldown = isTouchpad ? TOUCHPAD_COOLDOWN : MOUSE_KEYBOARD_COOLDOWN;

      if (currentTime - lastActionTime.current < cooldown) return;
      if (isAnimating.current) return;
      if (Math.abs(event.deltaY) < 10) return;

      lastActionTime.current = currentTime; // Lock the time
      const direction = event.deltaY > 0 ? 1 : -1;
      performScroll(direction);
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      const navKeys = [
        'ArrowUp',
        'ArrowDown',
        'PageUp',
        'PageDown',
        'Space',
        'Home',
        'End',
      ];
      if (navKeys.includes(event.code)) event.preventDefault();

      const currentTime = new Date().getTime();

      if (currentTime - lastActionTime.current < MOUSE_KEYBOARD_COOLDOWN)
        return;
      if (isAnimating.current) return;

      let shouldPerformAction = true;
      let direction = 0;

      switch (event.code) {
        case 'ArrowDown':
        case 'PageDown':
        case 'Space':
          direction = 1;
          break;
        case 'ArrowUp':
        case 'PageUp':
          direction = -1;
          break;
        case 'Home':
          direction = -activeSection;
          break;
        case 'End':
          direction = sectionsRef.current.length - 1 - activeSection;
          break;
        default:
          shouldPerformAction = false;
      }

      if (shouldPerformAction && direction !== 0) {
        performScroll(direction);
        lastActionTime.current = currentTime;
      } else if (shouldPerformAction) {
        // This handles cases where direction is 0 (e.g., already at home/end)
        lastActionTime.current = currentTime;
      }
    };

    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b;
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b;
    };

    // Handle mobile scroll tracking (passive, just for activeSection)
    const handleMobileScroll = () => {
      const currentScrollY = window.pageYOffset;
      let currentSectionIndex = 0;

      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (currentScrollY >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      if (currentSectionIndex !== activeSection) {
        setActiveSection(currentSectionIndex);
      }
    };

    if (!isMobile) {
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('keydown', handleKeyDown);
    } else {
      // On mobile, just track scroll position for activeSection
      window.addEventListener('scroll', handleMobileScroll, { passive: true });
    }

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('scroll', handleMobileScroll);
      if (animationFrameId.current)
        cancelAnimationFrame(animationFrameId.current);
    };
  }, [
    isMobile,
    activeSection,
    children,
    sectionConfigs,
    isSectionTallerThanViewport,
    isAtBottomOfSection,
    getSectionScrollProgress,
  ]);

  return (
    <>
      {/* Animation overlay for custom scroll directions */}
      <div
        ref={overlayRef}
        className="scroll-animation-overlay"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100vh',
          pointerEvents: 'none',
          zIndex: 25, // Lower than header z-index (30/50) so header stays on top
          visibility: 'hidden',
          transform: 'translateY(0)',
          overflow: 'hidden',
          background: 'white',
          transition: 'transform 0.8s cubic-bezier(0.76, 0, 0.24, 1)',
        }}
      ></div>

      <div>
        {React.Children.map(children, (child, index) => {
          const childElement = child as React.ReactElement;

          return (
            <div
              ref={(el) => {
                if (el) sectionsRef.current[index] = el;
              }}
            >
              {React.cloneElement(childElement, {
                ...childElement.props,
                isActive: index === activeSection,
              })}
            </div>
          );
        })}
      </div>
    </>
  );
};

export default PageWrapper;
