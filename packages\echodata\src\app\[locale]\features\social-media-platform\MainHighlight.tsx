import Union from '@hi7/assets/background/union-social.svg?url';
import UnionLarge from '@hi7/assets/background/union-whatsapp-large2.svg?url';

import UserExperience from '@hi7/assets/background/user-experience.svg?url';

import Hightlight1 from '@hi7/assets/icon/social-hightlight1.svg?url';
import Hightlight2 from '@hi7/assets/icon/social-hightlight2.svg?url';
import Hightlight3 from '@hi7/assets/icon/social-hightlight3.svg?url';
import Hightlight4 from '@hi7/assets/icon/social-hightlight4.svg?url';
import Hightlight5 from '@hi7/assets/icon/social-hightlight5.svg?url';

import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function WhyChooseUs({ dictionary }: DictionaryProps) {
  const highlights1 = [
    {
      icon: Hightlight1,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
          .Pricing.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
        .Pricing.desc,
    },
    {
      icon: Hightlight2,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
          .Fast.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
        .Fast.desc,
    },
  ];
  const highlights2 = [
    {
      icon: Hightlight3,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
          .Data.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
        .Data.desc,
    },
    {
      icon: Hightlight4,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
          .Global.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
        .Global.desc,
    },
    {
      icon: Hightlight5,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
          .Seamless.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.MainHighlight
        .Seamless.desc,
    },
  ];

  return (
    <>
      <div className="relative mt-[300px] h-auto w-full overflow-visible bg-[#F6E1E1] pb-[30dvh] lg:mt-0 lg:h-auto lg:overflow-hidden lg:bg-transparent lg:pb-[3vh]">
        <Image
          src={Union}
          alt="union"
          className="absolute top-[-259px] h-[260px] w-full lg:hidden"
          priority
        />
        <Image
          src={UnionLarge}
          alt="UnionLarge"
          className="absolute z-0 mt-[11vh] hidden h-auto w-full object-cover lg:block"
        />

        <div className="absolute top-[-260px] right-0 h-[240px] w-[39dvw] overflow-hidden rounded-tl-[40px] rounded-bl-[10vw] transition-[padding,top] duration-500 lg:top-0 lg:h-[73vh] lg:w-[34vw] 2xl:h-[73vh]">
          <Image
            fill
            src={UserExperience}
            alt="UserExperience"
            className="object-cover"
            priority
          />
        </div>
        <div className="relative z-10 h-auto w-full bg-[#F6E1E1] transition-[top] duration-500 lg:mt-[12vh] lg:h-auto lg:bg-transparent">
          <h3
            className={`${arsenal.className} absolute top-[-15dvh] ml-[8vw] w-[38vw] border-b border-[#047AFF] pt-0 pr-0 pb-[2dvh] text-[9dvw] font-bold text-[#047AFF] lg:static lg:w-[45vw] lg:pt-[10vh] lg:text-[4vw]`}
          >
            {
              dictionary.productSolution.whatsAppMarketingManagement
                .MainHighlight.title
            }
          </h3>
          <div>
            <ul className="ml-[8vw] flex w-[92vw] flex-col items-start gap-0 pt-[5vh] text-[#047AFF] lg:mt-[2vh] lg:w-[50vw] lg:flex-row lg:gap-[4vw]">
              {highlights1.length > 0 &&
                highlights1.map((highlight1, index) => (
                  <li
                    key={index}
                    className="mb-[45px] w-[84dvw] lg:mb-0 lg:w-[22vw]"
                  >
                    <div className="w-[10dvw] lg:w-[3vw]">
                      <Image
                        src={highlight1.icon}
                        alt="whatsap-icon"
                        className="w-[60px]"
                        priority
                      />
                    </div>
                    <h4 className="my-[1vh] w-full border-b border-[#047AFF] pb-[1vh] text-[6vw] font-semibold lg:my-[2vh] lg:pb-[2vh] lg:text-[1.7vw]">
                      {highlight1.title}
                    </h4>

                    <p className="text-[5dvw] leading-[1.4] font-light lg:text-[1.3vw]">
                      {highlight1.desc}
                    </p>
                  </li>
                ))}
            </ul>
            <ul className="mt-[1.5dvh] ml-[8vw] flex flex-col items-start gap-0 text-[#047AFF] lg:mt-[12dvh] lg:w-[100vw] lg:flex-row lg:gap-[4vw]">
              {highlights2.length > 0 &&
                highlights2.map((highlight2, index) => (
                  <li
                    key={index}
                    className="mb-[45px] w-[84dvw] lg:mb-0 lg:w-[22vw]"
                  >
                    <div className="w-[10dvw] lg:w-[3vw]">
                      <Image
                        src={highlight2.icon}
                        alt="whatsap-icon"
                        className="w-[60px]"
                        priority
                      />
                    </div>
                    <h4 className="my-[1vh] w-full border-b border-[#047AFF] pb-[1vh] text-[6vw] leading-[1] font-semibold lg:my-[2vh] lg:pb-[2vh] lg:text-[1.7vw]">
                      {highlight2.title}
                    </h4>

                    <p className="text-[5dvw] leading-[1.4] font-light lg:text-[1.3vw]">
                      {highlight2.desc}
                    </p>
                  </li>
                ))}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}

export default WhyChooseUs;
