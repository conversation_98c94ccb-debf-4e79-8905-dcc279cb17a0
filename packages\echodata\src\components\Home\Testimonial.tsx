'use client';

import BluePolygonBgMobile from '@hi7/assets/background/polygon-vector-blue-mobile.png';
import BluePolygonBg from '@hi7/assets/background/polygon-vector-blue.png';
import PinkPolygonBgMobile from '@hi7/assets/background/polygon-vector-pink-mobile.png';
import PinkPolygonBg from '@hi7/assets/background/polygon-vector-pink.png';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import TriggerAnimation from '../TriggerAnimation';
import type { ClientKey } from './config';
import { FEEDBACKS } from './config';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function Feedback({ dictionary }: DictionaryProps) {

  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, align: 'center' });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    const originalArrayList = emblaApi.scrollSnapList()
    const filteredArrayList = Array.from(new Set(originalArrayList));
    setScrollSnaps(filteredArrayList);
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback((index: number) => {
    if (!emblaApi) return;
    emblaApi.scrollTo(index);
  }, [emblaApi]);

  return (
    <TriggerAnimation>
      <div className="w-full">
        <div className="flex flex-col items-start justify-center px-20 pt-[50px] md:py-[70px] lg:mt-[100px]">
          <h2 className={`text-[#04227D] font-bold text-[40px] lg:text-[64px] ${arsenal.className}`}>
            {dictionary.home.client.title}
          </h2>
        </div>
      </div>

      <div className="-mt-5">

        <div ref={emblaRef} className="h-[680px] md:hidden touch-pan-x overflow-y-hidden overflow-x-auto">
          {/******* Mobile view ******/}
          <div className="grid grid-flow-col grid-rows-2 gap-x-15 px-6 embla__container">
            {FEEDBACKS.map(({ image, key }, index) => {
              const { name, role, desc } = dictionary.home.client[key as ClientKey];
              return (
                <div
                  key={index}
                  className={clsx(
                    'flex-none snap-start w-[80vw] max-w-[320px]',
                    index % 2 !== 0 ? '-mt-[70px]' : 'mt-0'
                  )}
                >
                  <div className="relative overflow-hidden content-evenly" style={{ aspectRatio: '4 / 5' }} >
                    <Image
                      fill
                      src={index % 2 === 0 ? PinkPolygonBgMobile : BluePolygonBgMobile}
                      alt={''}
                      className="object-contain w-full"
                    />
                    <div className={`relative flex flex-col items-start h-full px-6 py-10 ${index % 2 === 0 ? 'text-[#FF5542]' : 'text-[#04227D]'}`}>
                      <div className="flex-1 flex items-center justify-center -mb-15">
                        <p className="text-center text-[15px] font-[300] text-start">{desc}</p>
                      </div>
                      <div className="mt-auto flex items-center gap-4 text-sm mb-10">
                        <b>{name}</b>
                        <p>{role}</p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/******* Desktop view ******/}
        <div className="hidden md:flex select-none relative justify-center z-10 cursor-grab">
          <div className="absolute top-[0%] left-0 opacity-100">
            <div className='w-[30vw] h-[70vh] bg-linear-[90deg,#fff_30%,#ffffff00_100%]' />
          </div>
          <div className=" absolute top-0 right-0 opacity-100">
            <div className='w-[30vw] h-[70vh] bg-linear-[270deg,#fff_30%,#ffffff00_100%]' />
          </div>
        </div>
        <div ref={emblaRef} className="hidden md:block h-[70vh] overflow-hidden cursor-grab max-w-[1600px] mx-auto">
          <div className="flex flex-nowrap snap-x gap-6 px-10 embla__container">
            {FEEDBACKS.map(({ image, key }, index) => {
              const { name, role, desc } = dictionary.home.client[key as ClientKey];

              return (
                <div
                  key={index}
                  className=" select-none flex-none snap-start w-[320px] xl:w-[420px]"
                >
                  <div className="relative overflow-hidden mr-8 xl:mr-10" style={{ aspectRatio: '4 / 5' }}>
                    <Image
                      fill
                      src={index % 2 === 0 ? PinkPolygonBg : BluePolygonBg}
                      alt="polygon background"
                      className="object-contain w-full"
                    />
                    <div className={`relative z-10 flex flex-col items-start h-full px-6 py-10 xl:px-8  ${index % 2 === 0 ? 'text-[#FF5542]' : 'text-[#04227D]'}`}>
                      <div className="flex-1 flex items-center justify-start">
                        <p className="text-[22px] font-[300] md:text-[18px] lg:text-[18px] xl:text-[26px] xl:font-thin">{desc}</p>
                      </div>
                      <div className="mt-auto flex items-center text-start gap-1 text-sm xl:text-md">
                        <b>{name}</b>
                        <p>{role}</p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="flex justify-center mt-4 gap-2 md:hidden">
          {scrollSnaps.map((_, index) => (
            <button
              key={index}
              onClick={() => scrollTo(index)}
              className={`h-2 w-2 rounded-full ${index === (selectedIndex / 2) ? 'bg-blue-500' : 'bg-gray-300'
                }`}
            />
          ))}
        </div>



      </div>
    </TriggerAnimation>




  );
}

export default Feedback;


