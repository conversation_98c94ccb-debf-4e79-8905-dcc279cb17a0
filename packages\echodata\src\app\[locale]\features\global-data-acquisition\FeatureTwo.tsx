import Feature1 from '@hi7/assets/icon/feature1.svg';
import Feature2 from '@hi7/assets/icon/feature2.svg';
import Feature3 from '@hi7/assets/icon/feature3.svg';
import AnimationFrame from '@hi7/components/AnimationFrame';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'auto',
});

function Feature({ dictionary }: DictionaryProps) {
  const features = [
    {
      icon: Feature1,
      title:
        dictionary.features.globalDataAcquisition.feature2.globalDataAcquisition
          .title,
      desc: dictionary.features.globalDataAcquisition.feature2
        .globalDataAcquisition.desc,
    },
    {
      icon: Feature2,
      title:
        dictionary.features.globalDataAcquisition.feature2
          .customNumberSegmentation.title,
      desc: dictionary.features.globalDataAcquisition.feature2
        .customNumberSegmentation.desc,
    },
    {
      icon: Feature3,
      title:
        dictionary.features.globalDataAcquisition.feature2
          .globalNumberGeneration.title,
      desc: dictionary.features.globalDataAcquisition.feature2
        .globalNumberGeneration.desc,
    },
  ];

  return (
    <div className="relative z-10 mt-15 flex items-center justify-center overflow-hidden rounded-[30px] bg-[#047AFF] px-8 lg:mx-16 lg:mb-15 lg:translate-y-[10%] lg:rounded-[80px] xl:mx-30 xl:translate-y-[20%]">
      <div className="h-full w-full lg:h-[88vh]">
        <div className="relative">
          <AnimationFrame
            variant="FadeIn"
            once={false}
            className="hidden lg:block"
          >
            <div className="flex flex-col items-start justify-center pt-12 pb-15 text-white lg:items-start lg:px-10 lg:pt-20 lg:pb-12 xl:pt-26 xl:pb-12">
              <h2
                className={`mb-8 text-[40px] leading-[40px] font-bold lg:mb-0 lg:translate-y-[-30%] lg:text-[4vw] lg:leading-[58px] ${arsenal.className}`}
              >
                {dictionary.features.globalDataAcquisition.feature2.title}
              </h2>
              <p className="w-[70%] text-[16px] font-thin whitespace-pre-line lg:text-[1.5vw] lg:font-[300] xl:w-[40%]">
                {dictionary.features.globalDataAcquisition.feature2.desc}
              </p>
              <hr className="mt-5 w-full lg:my-7" />
              <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:mt-5 lg:grid-cols-3 lg:gap-7 xl:gap-13">
                {features.length > 0 &&
                  features.map((feature, index) => (
                    <div className="mb-0 flex w-full flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 text-[#047AFF] lg:rounded-[20px] lg:p-6 xl:p-10">
                      <div className="flex flex-row items-center justify-center xl:justify-start">
                        <feature.icon className="w-[30%] xl:w-[15%] xl:scale-125" />
                        <h1 className="mt-1 text-[20px] leading-[25px] font-[500] lg:text-[20px] xl:text-[25px]">
                          {feature.title}
                        </h1>
                      </div>
                      <hr />
                      <span className="text-[16px] font-[300] xl:text-[22px] xl:font-[400]">
                        {feature.desc}
                      </span>
                    </div>
                  ))}
              </div>
            </div>
          </AnimationFrame>
        </div>
      </div>
    </div>
  );
}

export default Feature;
