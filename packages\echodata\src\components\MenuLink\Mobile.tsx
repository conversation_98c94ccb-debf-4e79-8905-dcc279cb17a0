'use client';

import Link from '@hi7/components/Link';
import SubMenuLink from '@hi7/components/SubMenuLink/Mobile';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';

const MenuLink = (props: MenuLinkProps) => {
  const { url, asButton: button, onClick, children, items = [] } = props;
  const pathname = usePathname();

  const hasSubitem = items.length > 0;
  if (hasSubitem) {
    return <SubMenuLink {...props} />;
  }

  return (
    <Link
      onClick={onClick}
      url={url}
      className={clsx(
        'justify-end text-[24px] font-[500]',
        button
          ? clsx(
            'text-[#FF5542]',
          )
          : 'py-2',
        pathname === url || pathname.includes(url) ? 'text-hi7-primary' : '',
        'flex cursor-pointer items-center gap-2.5 ',
      )}
    >
      {children}
    </Link>
  );
};

export default MenuLink;
