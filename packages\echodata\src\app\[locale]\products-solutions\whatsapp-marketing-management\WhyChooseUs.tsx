import Union from '@hi7/assets/background/union-whatsapp.svg?url';
import Reason1 from '@hi7/assets/icon/reason1.svg?url';
import Reason2 from '@hi7/assets/icon/reason2.svg?url';
import Reason3 from '@hi7/assets/icon/reason3.svg?url';
import Whatsapp from '@hi7/assets/icon/whatsapp2.svg?url';
import AnimationFrame from '@hi7/components/AnimationFrame';

import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function WhyChooseUs({ dictionary }: DictionaryProps) {
  const reasons = [
    {
      icon: Reason1,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.whyChooseUs
          .PreciseTarget.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.whyChooseUs
        .PreciseTarget.desc,
    },
    {
      icon: Reason2,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.whyChooseUs.Boost
          .title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.whyChooseUs
        .Boost.desc,
    },
    {
      icon: Reason3,
      title:
        dictionary.productSolution.whatsAppMarketingManagement.whyChooseUs
          .LowCustomer.title,
      desc: dictionary.productSolution.whatsAppMarketingManagement.whyChooseUs
        .LowCustomer.desc,
    },
  ];

  return (
    <>
      <div className="mt-[1vh] h-auto w-full overflow-hidden lg:mt-0 lg:h-screen">
        <div className="hidden h-[29.9dvh] w-full lg:block">
          <Image
            src={Union}
            alt="union"
            className="h-[29.9dvh] w-full"
            priority
          />
        </div>
        <div className="relative h-auto w-full rounded-[10dvw] rounded-tl-none bg-[#E9F3FF] pb-[80px] lg:h-[70vh] lg:pb-0">
          <AnimationFrame
            variant="SlideTopRightEase"
            once={false}
            className="absolute top-[-50vh] right-[6vw] hidden gap-[10vw] lg:flex"
          >
            <div className="h-[50vh] w-[10vw] rotate-45 transform rounded-[5vw] bg-[#E9F3FF]"></div>
            <div className="h-[50vh] w-[10vw] rotate-45 transform rounded-[5vw] bg-[#E9F3FF]"></div>
            <div className="mt-[25vh] ml-[-5vw] h-[10vw] w-[10vw] rounded-[50%] bg-[#E9F3FF]"></div>
          </AnimationFrame>
          <div className="relative mx-auto mt-[5dvh] h-auto w-[90dvw] lg:mt-0 lg:h-[70vh] lg:pr-[7vw]">
            <Image
              src={Whatsapp}
              alt="whatsapp"
              className="absolute top-[-23%] hidden w-[7vw] lg:block"
            />
            <h2
              className={`${arsenal.className} mb-[2vh] border-0 border-[#047AFF] pt-[9vh] pb-0 text-left text-[37px] leading-[38.4px] font-bold whitespace-pre-line text-[#047AFF] lg:border-b lg:pt-[2vh] lg:pb-[4vh] lg:text-[4.3vw] lg:leading-[1]`}
            >
              {
                dictionary.productSolution.whatsAppMarketingManagement
                  .whyChooseUs.title
              }
            </h2>
            <ul className="mt-0 flex w-[90dvw] flex-wrap justify-between text-[#047AFF] lg:gap-[1vw] lg:pr-[7vw] lg:pb-[4vh]">
              {reasons.length > 0 &&
                reasons.map((reason, index) => (
                  <li key={index} className="w-full pt-[2dvh] lg:w-[23vw]">
                    <div className="mb-[2dvh] flex h-[12dvh] w-full items-center border-b border-[#047AFF]">
                      <Image
                        src={reason.icon}
                        alt="whatsap-icon"
                        className="mr-[5dvw] ml-[0.5dvw] w-[13dvw] lg:mr-[2vw] lg:w-[3dvw]"
                        priority
                      />
                      <h4 className="flex-1 text-[6.1dvw] leading-[1] font-medium whitespace-pre-line lg:text-[1.8vw]">
                        {reason.title}
                      </h4>
                    </div>

                    <div>
                      <p className="text-[4.6dvw] leading-[1.4] font-normal lg:text-[1.2vw]">
                        {reason.desc}
                      </p>
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}

export default WhyChooseUs;
