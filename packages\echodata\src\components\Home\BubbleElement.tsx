import Bubbles from '@hi7/assets/background/bubble-element.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import AnimationFrameInOut from '../AnimationFrameInOut';

function BubbleElement({ dictionary }: DictionaryProps) {
  return (
    <section className='hidden overflow-hidden lg:block h-screen w-screen pt-15 lg:translate-y-[-8%] xl:translate-y-[0%]'>
      <AnimationFrameInOut
        variant="SlideInRight45DegreeFurther2"
        outVariant="SlideInRight45Degree"
        once={false}
        className="w-full lg:w-[150vw] h-screen overflow-visible"
      >
        <Bubbles className="ml-[280px] lg:scale-100 xl:scale-135 xl:mt-28 xl:ml-[500px] -rotate-45" />
      </AnimationFrameInOut>
    </section>
  );
}

export default BubbleElement;
