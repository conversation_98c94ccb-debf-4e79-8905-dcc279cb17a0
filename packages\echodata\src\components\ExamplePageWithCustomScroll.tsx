import PageWrapper from '@hi7/components/PageWrapper';
import type { PageWrapperSectionProps } from '@hi7/interface/page-wrapper';
import type { Dictionary } from '@hi7/interface/dictionary';

// Example section components with scrollDirection prop
const SectionOne = ({ isActive, isMobile }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-blue-500 flex items-center justify-center">
    <div className="text-white text-center">
      <h1 className="text-4xl font-bold mb-4">Section One</h1>
      <p className="text-xl">Normal scroll behavior - goes up when scrolling down</p>
      <p className="text-sm mt-4">Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}</p>
    </div>
  </div>
);

const SectionTwo = ({ isActive, isMobile, scrollDirection }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-green-500 flex items-center justify-center">
    <div className="text-white text-center">
      <h1 className="text-4xl font-bold mb-4">Section Two</h1>
      <p className="text-xl">Custom scroll behavior - goes DOWN when scrolling down</p>
      <p className="text-sm mt-4">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'} | 
        Scroll Direction: {scrollDirection}
      </p>
    </div>
  </div>
);

const SectionThree = ({ isActive, isMobile }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-purple-500 flex items-center justify-center">
    <div className="text-white text-center">
      <h1 className="text-4xl font-bold mb-4">Section Three</h1>
      <p className="text-xl">Normal scroll behavior - goes up when scrolling down</p>
      <p className="text-sm mt-4">Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}</p>
    </div>
  </div>
);

interface ExamplePageProps {
  dictionary: Dictionary;
}

export default function ExamplePageWithCustomScroll({ dictionary }: ExamplePageProps) {
  return (
    <PageWrapper>
      <SectionOne />
      <SectionTwo scrollDirection="down" />
      <SectionThree />
    </PageWrapper>
  );
}
