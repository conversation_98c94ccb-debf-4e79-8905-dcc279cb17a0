import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function GetStarted({ dictionary }: DictionaryProps) {
  return (
    <div className="flex items-center justify-center overflow-hidden bg-linear-[180deg,#FFFFFF_0%,#A6C4EE_25%,#04227D_50%,#04227D_100%] text-white h-screen md:bg-linear-[180deg,#04227D_100%] md:h-[420px] lg:translate-y-[15%]">
      <div className="w-full">
        <div className="relative min-h-[450px]">
          <div className="flex flex-col items-center justify-center pt-[84px] text-center px-15 lg:px-5 lg:pt-[165px] xl:pt-[200px]">
            <h2 className={`${arsenal.className} mb-2.5 text-[40px] leading-[42px] font-bold lg:text-[64px] lg:leading-[60px]`}>
              {dictionary.general.freeTrial.title}
            </h2>
            <p className="text-[14px] leading-[16px] lg:text-[20px] lg:leading-[28px]">
              {dictionary.general.freeTrial.desc}
            </p>
          </div>
          <div className="mt-5 flex flex-col items-center justify-center md:flex-row md:gap-5">
            <a
              href="https://scrmchampion.com/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="block w-auto text-center cursor-pointer rounded-[25px] bg-[#FF5542] py-0 px-[30px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[45px]"
            >
              {dictionary.general.freeTrial.button1}
            </a>
            <a
              href="https://scrmchampion.com/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-5 block w-auto text-center cursor-pointer rounded-[25px] bg-[#047AFF] py-0 px-[30px] whitespace-nowrap text-white hover:bg-[#C7E5FF] font-bold text-[18px] leading-[45px] md:mt-0 lg:mt-0"
            >
              {dictionary.general.freeTrial.button5}
            </a>

          </div>
        </div>
      </div>
    </div >
  );
}

export default GetStarted;
