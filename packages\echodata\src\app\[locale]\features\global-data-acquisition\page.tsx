import Landing from '@hi7/app/[locale]/features/global-data-acquisition/Landing';
import GetStarted from '@hi7/components/Home/GetStarted';
import PageWrapper from '@hi7/components/PageWrapper';
import type { Dictionary } from '@hi7/interface/dictionary';
import { getDictionary, type Locale } from '@hi7/lib/i18n';
import FeatureOne from './FeatureOne';
import FeatureTwo from './FeatureTwo';

type PageProps = {
  params: {
    locale: Locale;
  };
};

export default async function Page({ params }: PageProps) {
  const { locale } = params;
  const t: Dictionary = await getDictionary(locale);

  const sectionConfigs = [
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const },
  ];

  return (
    <PageWrapper sectionConfigs={sectionConfigs}>
      <Landing dictionary={t} />
      <FeatureOne dictionary={t} />
      <FeatureTwo dictionary={t} />
      <GetStarted dictionary={t} />
    </PageWrapper>
  );
}
