// import Feature3 from '@hi7/assets/icon/feature5.svg';
import InsightImage from '@hi7/assets/background/insight-6.png';
import PolygonBg from '@hi7/assets/background/polygon-bg.png';
import Feature1 from '@hi7/assets/icon/feature6.svg';
import Feature2 from '@hi7/assets/icon/feature7.svg';
import Feature3 from '@hi7/assets/icon/feature8.svg';
import Feature4 from '@hi7/assets/icon/feature9.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import AnimationFrame from '../AnimationFrame';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function Feature({ dictionary }: DictionaryProps) {

  const features = [
    {
      icon: Feature1,
      title: dictionary.home.feature2.globalCoverage.title,
      desc: dictionary.home.feature2.globalCoverage.desc,
    },
    {
      icon: Feature2,
      title: dictionary.home.feature2.lowestPrices.title,
      desc: dictionary.home.feature2.lowestPrices.desc,
    },
    {
      icon: Feature3,
      title: dictionary.home.feature2.accurateFiltering.title,
      desc: dictionary.home.feature2.accurateFiltering.desc,

    },
    {
      icon: Feature4,
      title: dictionary.home.feature2.ecosystemIntegration.title,
      desc: dictionary.home.feature2.ecosystemIntegration.desc,
    }
  ];

  return (
    <TriggerAnimation>
      <section className="relative overflow-x-clip text-[#047AFF] flex flex-col items-center justify-between gap-10 px-10 py-10 mt-5 bg-transparent md:bg-[#E9F3FF] md:rounded-[60px] lg:translate-y-[15%] lg:flex-row lg:bg-transparent lg:px-0 lg:pb-20 lg:pt-25">
        <svg
          className='hidden absolute inset-0 z-[-1] pointer-events-none lg:block'
          width="100%"
          height="100vh"
          viewBox="0 0 1441 856"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
        >
          <path d="M0.5 126.95V161.11C0.5 227.91 54.6688 282.06 121.492 282.06H265C463 282.06 587.372 389 587.372 593C587.372 623.331 587.375 615.5 587.375 616.335C587.375 698 639 756 731.5 756H1289.5C1377.5 756 1441 695.5 1441 608V126.95C1441 60.1496 1441 85.9995 1441 85.9995H0.51001C0.51001 85.9995 0.51001 60.1496 0.51001 126.95H0.5Z" fill="#E9F3FF" />
          <path d="M1 80C1 121.974 35.0264 156 77 156H1365C1406.97 156 1441 121.974 1441 80C1441 38.0264 1406.97 4 1365 4H77C35.0263 4 1 38.0264 1 80Z" fill="#E9F3FF" />
        </svg>
        {/* <PolygonBg className="absolute inset-0 z-[-1] pointer-events-none md:hidden" /> */}

        <Image src={PolygonBg} alt={'insight-image'} className="w-[100%] absolute object-cover inset-0 z-[-1] pointer-events-none h-[100%] md:hidden" />
        <div className="relative w-full pt-10 lg:w-1/2 mb-10 lg:mb-0 lg:self-start lg:pt-0">
          <h2 className={`hidden ml-10 leading-[45px] font-bold mb-10 ${arsenal.className} md:flex md:text-[50px] md:mb-5 lg:text-[64px] lg:ml-15 xl:pl-[100px]`}>{dictionary.home.feature2.title} </h2>
          <h2 className={`text-[40px] lg:text-5xl leading-[45px] font-bold mb-10 ${arsenal.className} md:hidden`}>{dictionary.home.feature2.title.split(' ').map((line, i) => <h2 key={i}>{line}<br /></h2>)}</h2>
          <p>{dictionary.home.feature2.desc}</p>

          <AnimationFrame
            variant="SlideInHigh"
            once={false}
            className="md:hidden"
          >
            <Image src={InsightImage} alt={'insight-image'} className=" w-[100%] absolute -right-38 -top-32 lg:w-full lg:scale-115 lg:rounded-[200px] lg:left-35 lg:top-15 lg:-translate-x-2/5 lg:w-[600px] xl:top-50 xl:-translate-x-1/3" />
          </AnimationFrame>
        </div>

        <div className="w-full grid gap-6 -mt-8 lg:mt-0 lg:w-5/7 xl:w-5/9 xl:self-start">
          <div className="grid gap-8 md:grid-cols-2 md:gap-10 xl:gap-20 lg:gap-10 lg:px-15">
            {features.map((feature, i) => (
              <div key={i} className='mb-5'>
                <div className="flex items-center justify-start gap-5 font-[500] text-[24px] leading-[30px]">
                  <feature.icon className="w-14 h-14 lg:w-15 lg:h-15 " />
                  <span className="items-start">{feature.title}</span>
                </div>
                <hr className="my-5 xl:my-3" />
                <p className="text-[18px] font-[400] lg:text-[16px] xl:text-[18px]">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </TriggerAnimation >
  );
}

export default Feature;
