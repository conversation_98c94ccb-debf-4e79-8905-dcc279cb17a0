import BannerLongRoundVector from '@hi7/assets/background/elongated-round-vector.svg';
import BannerBgImg from '@hi7/assets/background/global-accqui-bg-img.png';
import BannerBgImgMobile from '@hi7/assets/background/global-accqui-bg-mobile-img.png';
import BannerRoundVector from '@hi7/assets/background/rounded-vector.svg';
import AnimationFrameInOut from '@hi7/components/AnimationFrameInOut';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Landing({ dictionary }: DictionaryProps) {
  return (
    <div className="relative flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="h-screen md:h-[50vh] lg:h-[100vh]">
          <div className="w-full items-center justify-center lg:flex">
            <Image
              src={BannerBgImg}
              alt={'background'}
              className="absolute top-0 left-0 z-0 hidden md:block lg:h-[95vh] xl:w-screen"
            />
            <Image
              src={BannerBgImgMobile}
              alt={'background'}
              className="h-full w-full object-cover md:hidden"
            />
            <div className="animate-slide-in-down absolute top-10 left-10 z-10 w-[90%] text-start whitespace-break-spaces text-white transition-opacity duration-700 lg:top-30 xl:left-24">
              <h1
                className={`text-[48px] leading-[50px] font-bold tracking-[1px] lg:text-[64px] xl:mb-9 xl:text-[77px] xl:leading-[68px] ${arsenal.className}`}
              >
                {dictionary.features.globalDataAcquisition.banner.title}
              </h1>
              <hr className="my-4 w-[90%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div className="grid grid-cols-[30%_70%] items-center gap-2 lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5">
                <div className="items-center justify-center self-center text-right">
                  <p className="text-[14px] leading-[20px] font-[400] lg:text-[20px] xl:text-[29px] xl:leading-[30px]">
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point1Title
                    }
                  </p>
                </div>
                <h3 className="mb-0 flex flex-col items-baseline md:flex-row">
                  <span className="text-[28px] leading-[35px] font-bold lg:text-[48px]">
                    200+
                  </span>
                  <span className="text-[20px] leading-[20px] font-thin lg:text-[48px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point1Desc1
                    }
                  </span>
                  <span className="text-[12px] leading-[20px] font-thin lg:text-[20px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point1Desc2
                    }
                  </span>
                </h3>
              </div>
              <hr className="my-4 w-[90%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div className="grid grid-cols-[30%_70%] items-center gap-2 lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5">
                <div className="items-center justify-center self-center text-right">
                  <p className="text-[14px] leading-[20px] font-[400] lg:text-[20px] xl:text-[29px] xl:leading-[30px]">
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point2Title
                    }
                  </p>
                </div>
                <h3 className="mb-0 flex flex-col items-baseline md:flex-row">
                  <span className="text-[28px] leading-[35px] font-bold lg:text-[48px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point2Desc1
                    }
                  </span>
                  <span className="text-[20px] leading-[20px] font-thin lg:text-[48px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point2Desc2
                    }
                  </span>
                  <span className="text-[12px] leading-[20px] font-thin lg:text-[20px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point2Desc3
                    }
                  </span>
                </h3>
              </div>
              <hr className="my-4 w-[90%] md:mx-auto md:w-full lg:my-5 xl:my-8" />
              <div className="grid grid-cols-[30%_70%] items-center gap-2 lg:grid-cols-[20%_80%] lg:gap-5 lg:pl-5">
                <div className="items-center justify-center self-center text-right">
                  <p className="text-[14px] leading-[20px] font-[400] lg:text-[20px] xl:text-[29px] xl:leading-[30px]">
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point3Title
                    }
                  </p>
                </div>
                <h3 className="mb-0 flex flex-col items-baseline md:flex-row">
                  <span className="text-[28px] leading-[35px] font-bold lg:text-[48px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point3Desc1
                    }
                  </span>
                  <span className="text-[20px] leading-[20px] font-thin lg:text-[48px]">
                    {' '}
                    {
                      dictionary.features.globalDataAcquisition.banner
                        .point3Desc2
                    }
                  </span>
                </h3>
              </div>
              <hr className="my-4 w-[100%] md:mx-auto md:w-full" />
              <div className="block lg:flex">
                <a
                  href="#"
                  target="_blank"
                  className="mt-5 block w-auto max-w-[200px] cursor-pointer rounded-[25px] bg-[#FF5542] px-[20px] py-1 text-center text-[18px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-[#C7E5FF]"
                >
                  {dictionary.general.freeTrial.button1}
                </a>
                <a
                  href="https://scrmchampion.com/contact"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-5 block w-auto max-w-[200px] cursor-pointer rounded-[25px] bg-[#047AFF] px-[20px] py-1 text-center text-[18px] leading-[40px] font-bold whitespace-nowrap text-white hover:bg-[#C7E5FF] lg:ml-5"
                >
                  {dictionary.general.freeTrial.button5}
                </a>
              </div>
            </div>
            <BannerLongRoundVector className="absolute -right-43 -bottom-13 scale-55 md:hidden" />
            <BannerRoundVector className="absolute right-16 bottom-10 scale-55 md:hidden" />
          </div>
          <AnimationFrameInOut
            variant="SlideInRight45Degree"
            outVariant="SlideInRight45DegreeFurther"
            once={false}
            className="hidden lg:block"
          >
            <BannerLongRoundVector className="md:absolute lg:top-115 lg:right-80 lg:scale-70 xl:top-200 xl:left-330 xl:scale-130" />
            <BannerLongRoundVector className="md:absolute lg:top-115 lg:right-25 lg:scale-70 xl:top-200 xl:left-240 xl:scale-130" />
            <BannerRoundVector className="right-0 scale-80 md:absolute lg:top-125 lg:right-15 xl:top-190 xl:left-410 xl:scale-130" />
          </AnimationFrameInOut>
        </div>
      </div>
    </div>
  );
}

export default Landing;
