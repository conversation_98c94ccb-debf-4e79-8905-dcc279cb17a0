import PageWrapper from '@hi7/components/PageWrapper';
import type { PageWrapperSectionProps } from '@hi7/interface/page-wrapper';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';

// Test section components
const HeroSection = ({ isActive, isMobile }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-gradient-to-br from-blue-600 to-blue-800 flex items-center justify-center text-white">
    <div className="text-center max-w-4xl px-6">
      <h1 className="text-5xl md:text-7xl font-bold mb-6">
        Enhanced PageWrapper
      </h1>
      <p className="text-xl md:text-2xl mb-8">
        Test page for custom scroll directions
      </p>
      <div className="text-sm opacity-75">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}
      </div>
      <div className="mt-8 text-lg">
        ⬇️ Scroll down to see normal behavior (section slides up)
      </div>
    </div>
  </div>
);

const SpecialSection = ({ isActive, isMobile, scrollDirection }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-gradient-to-br from-green-600 to-green-800 flex items-center justify-center text-white">
    <div className="text-center max-w-4xl px-6">
      <h1 className="text-5xl md:text-7xl font-bold mb-6">
        Special Section
      </h1>
      <p className="text-xl md:text-2xl mb-8">
        This section has <code className="bg-black/20 px-2 py-1 rounded">scrollDirection="down"</code>
      </p>
      <div className="text-lg mb-4">
        When you scroll down from this section, it will slide DOWN instead of up!
      </div>
      <div className="text-sm opacity-75 mb-8">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'} | 
        Direction: {scrollDirection || 'default'}
      </div>
      <div className="text-lg">
        ⬇️ Scroll down to see the custom DOWN animation
      </div>
    </div>
  </div>
);

const FinalSection = ({ isActive, isMobile }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center text-white">
    <div className="text-center max-w-4xl px-6">
      <h1 className="text-5xl md:text-7xl font-bold mb-6">
        Final Section
      </h1>
      <p className="text-xl md:text-2xl mb-8">
        Back to normal scroll behavior
      </p>
      <div className="text-sm opacity-75 mb-8">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}
      </div>
      <div className="text-lg">
        ⬆️ Scroll up to test the animations in reverse
      </div>
    </div>
  </div>
);

async function TestScrollDirectionsPage({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  return (
    <PageWrapper>
      <HeroSection />
      <SpecialSection scrollDirection="down" />
      <FinalSection />
    </PageWrapper>
  );
}

export default TestScrollDirectionsPage;
