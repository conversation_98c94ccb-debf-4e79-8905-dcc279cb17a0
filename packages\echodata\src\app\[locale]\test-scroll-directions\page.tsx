import PageWrapper from '@hi7/components/PageWrapper';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import type { PageWrapperSectionProps } from '@hi7/interface/page-wrapper';
import { getDictionary } from '@hi7/lib/i18n';

// Test section components
const HeroSection = ({ isActive, isMobile }: PageWrapperSectionProps) => (
  <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-600 to-blue-800 text-white">
    <div className="max-w-4xl px-6 text-center">
      <h1 className="mb-6 text-5xl font-bold md:text-7xl">
        Enhanced PageWrapper
      </h1>
      <p className="mb-8 text-xl md:text-2xl">
        Test page for custom scroll directions
      </p>
      <div className="text-sm opacity-75">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}
      </div>
      <div className="mt-8 text-lg">
        ⬇️ Scroll down to see normal behavior (section slides up)
      </div>
    </div>
  </div>
);

const SpecialSection = ({
  isActive,
  isMobile,
  scrollDirection,
}: PageWrapperSectionProps) => (
  <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-green-600 to-green-800 text-white">
    <div className="max-w-4xl px-6 text-center">
      <h1 className="mb-6 text-5xl font-bold md:text-7xl">Special Section</h1>
      <p className="mb-8 text-xl md:text-2xl">
        This section has{' '}
        <code className="rounded bg-black/20 px-2 py-1">
          scrollDirection="down"
        </code>
      </p>
      <div className="mb-4 text-lg">
        When you scroll down from this section, it will slide DOWN instead of
        up!
      </div>
      <div className="mb-8 text-sm opacity-75">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'} |
        Direction: {scrollDirection || 'default'}
      </div>
      <div className="text-lg">
        ⬇️ Scroll down to see the custom DOWN animation
      </div>
    </div>
  </div>
);

const FinalSection = ({ isActive, isMobile }: PageWrapperSectionProps) => (
  <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-purple-600 to-purple-800 text-white">
    <div className="max-w-4xl px-6 text-center">
      <h1 className="mb-6 text-5xl font-bold md:text-7xl">Final Section</h1>
      <p className="mb-8 text-xl md:text-2xl">Back to normal scroll behavior</p>
      <div className="mb-8 text-sm opacity-75">
        Active: {isActive ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}
      </div>
      <div className="text-lg">
        ⬆️ Scroll up to test the animations in reverse
      </div>
    </div>
  </div>
);

async function TestScrollDirectionsPage({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);

  const sectionConfigs = [
    { scrollDirection: 'default' as const }, // HeroSection
    { scrollDirection: 'down' as const }, // SpecialSection
    { scrollDirection: 'default' as const }, // FinalSection
  ];

  return (
    <PageWrapper sectionConfigs={sectionConfigs}>
      <HeroSection />
      <SpecialSection />
      <FinalSection />
    </PageWrapper>
  );
}

export default TestScrollDirectionsPage;
