'use client';
import b_left from '@hi7/assets/background/b_left.svg?url';
import b_mid from '@hi7/assets/background/b_mid.svg?url';
import b_right from '@hi7/assets/background/b_right.svg?url';
import btm_union from '@hi7/assets/background/btm_union.svg?url';
import industryInsights from '@hi7/assets/background/industry-insights.svg?url';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { Locale } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { Arsenal, Heebo } from 'next/font/google';
import Image from 'next/image';

interface HeroProps extends DictionaryProps {
  locale: Locale;
}

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

const heebo = Heebo({
  subsets: ['latin'],
  display: 'swap',
});

const Hero = ({ dictionary, locale }: HeroProps) => {
  return (
    <>
      <div className="relative h-auto w-full rounded-bl-[8vw] bg-[#04227D] md:h-[88vh]">
        <div className="pt-11 pb-[50px]">
          <div className="flex flex-col justify-between gap-10 pt-[5vh] md:flex-row md:pt-[10vh] 2xl:gap-x-15">
            <div className="relative block h-[25vh] w-[92%] flex-col justify-between md:flex md:h-[60vh] md:w-[38%]">
              <h1
                className={`mb-7 text-right text-[10vw] leading-none font-bold tracking-tight text-white md:mb-3 md:w-full md:text-[5.1vw] ${arsenal.className}`}
              >
                {dictionary.industryInsights.title}
              </h1>
              <div className="mt-4 flex h-auto flex-col justify-end self-end rounded-tr-[40px] bg-white pr-[8vw] text-right text-[#04227D] md:h-full md:w-full md:gap-1 md:pr-[4.5vw] 2xl:rounded-tr-[60px]">
                <div className="mt-6 border-b border-[#04227D] pb-2 text-[4vw] tracking-tight whitespace-pre-line md:pb-[2vh] md:text-[1.6vw] 2xl:mt-0">
                  <p
                    dangerouslySetInnerHTML={{
                      __html: dictionary.industryInsights.union.top,
                    }}
                  ></p>
                </div>
                <div
                  className={clsx(
                    'mb-[2vh] flex border-[#04227D] text-[13vw] font-bold tracking-tighter md:justify-end md:text-[4.5vw]',
                    locale === 'en'
                      ? 'items-center justify-center border-b'
                      : 'items-baseline justify-end',
                  )}
                >
                  <span>{dictionary.industryInsights.union.middle1}</span>
                  <p
                    className={clsx(
                      'leading-7 font-thin tracking-tight 2xl:leading-10',
                      locale === 'en'
                        ? 'ml-[1.5vw] w-[10vw] text-left text-[6vw] md:text-[2.5vw]'
                        : 'w-[24vw] text-[12vw] md:w-[10vw] md:text-[4.5vw]',
                      heebo.className,
                    )}
                  >
                    {dictionary.industryInsights.union.middle2}
                  </p>
                </div>
                {locale === 'en' && (
                  <p className="mt-[-15px] text-xs md:mt-[-5px] md:text-[1.5vw]">
                    {dictionary.industryInsights.union.bottom}
                  </p>
                )}
              </div>
              <div className="mt-[-1px]">
                <div className="relative flex h-[5.5vh] justify-end md:h-full md:w-full">
                  <button className="absolute top-[20%] right-[44%] flex h-[80%] w-[40%] cursor-pointer items-center justify-center rounded-4xl bg-[#FF5542] text-[80%] font-bold whitespace-nowrap text-white transition duration-200 hover:bg-[#04217d] hover:text-[#FF5542] md:right-[53%] md:w-[33%] md:px-5 md:text-[1.1vw]">
                    {dictionary.industryInsights.button}
                  </button>
                  <Image
                    src={btm_union}
                    alt="union"
                    className="h-full w-[47%] md:w-[60%]"
                  />
                </div>
              </div>
            </div>

            <div className="mt-[50px] ml-[5%] h-auto w-[95%] overflow-hidden rounded-l-4xl md:mt-0 md:ml-0 md:h-[60vh] md:w-[60vw]">
              <Image
                src={industryInsights}
                alt="industry-insights"
                className="h-auto w-full object-cover md:h-[60vh]"
              />
            </div>
          </div>
        </div>
        <div className="absolute right-2 bottom-[2vh] z-10 h-[4vw]">
          <div className="hidden pt-3 text-right md:flex">
            <Image src={b_left} alt="button-left" className="2xl:w-[3vw]" />
            <Image
              src={b_right}
              alt="button-right"
              className="ml-[-5px] 2xl:m-0 2xl:w-[3vw]"
            />
          </div>
          <Image src={b_mid} alt="3button" className="block md:hidden" />
        </div>
      </div>
    </>
  );
};

export default Hero;
