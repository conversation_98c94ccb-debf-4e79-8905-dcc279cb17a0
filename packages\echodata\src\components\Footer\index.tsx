'use client';

import Telegram from '@hi7/assets/icon/telegram.svg';
import Whatsapp from '@hi7/assets/icon/whatsapp.svg';
import X from '@hi7/assets/icon/xtwitter.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import type { Dictionary } from '@hi7/interface/dictionary';
import type { MediaProps, MenuLinkProps } from '@hi7/interface/link';
import { type Locale } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import Link from '../Link';

type FooterProps = {
  dictionary: Dictionary;
  currentLocale: Locale;
};

const Footer = ({ dictionary }: FooterProps) => {
  const pathname = usePathname();

  // const showReward = pathname.split('/').length === 2; // is home page

  const links: MenuLinkProps[] = [
    {
      url: '/products-solutions/whatsapp-marketing-management',
      children: dictionary.footer.whatsappMarkting,
    },
    {
      url: '/products-solutions/social-media-platform',
      children: dictionary.footer.socialMarkting,
    },
    {
      url: '/products-solutions/customer-service-saas-tool',
      children: dictionary.footer.saasTool,
    },
    {
      url: '/products-solutions/customer-management',
      children: dictionary.footer.customerManagement,
    },
  ];

  const recommendations: MenuLinkProps[] = [
    { url: '#', children: '007TG' },
    { url: '#', children: dictionary.footer.globalData },
    { url: '#', children: dictionary.footer.industryInfo },
  ];

  const medias: MediaProps[] = [
    {
      icon: <Telegram />,
      url: 'https://www.instagram.com/hisevendm/',
      target: '_blank',
    },
    {
      icon: <X width="21px" />,
      url: 'https://www.x.com/hisevendm/',
      target: '_blank',
    },
    {
      icon: <Whatsapp />,
      url: 'https://my.linkedin.com/company/hiseven?trk=public_post_feed-actor-name',
      target: '_blank',
    },
  ];

  const tnc: MenuLinkProps[] = [
    {
      url: '/privacy',
      target: '_blank',
      children: dictionary.footer.privacy,
    },
    {
      url: '/terms',
      target: '_blank',
      children: dictionary.footer.terms,
    },
  ];

  return (
    <div
      className={clsx('lg:snap-start', 'bg-[#04227D] text-white', 'relative')}
    >
      <hr className="border-t border-white" />
      <div className="mx-auto px-4 pt-[120px] pb-[60px] text-[14px] leading-[20px] lg:px-25">
        <div className="grid grid-cols-1 gap-7">
          {/** First Row */}
          <div className="flex flex-col gap-5 lg:flex-row lg:gap-[50px]">
            <div className="flex flex-col items-start gap-5 lg:flex-[0.6] xl:flex-[0.9]">
              <Logo height={40} className="ml-7 scale-[1.5]" />
              <span className="text-[14px] font-[400] lg:mb-5">
                {dictionary.footer.overview}
              </span>
            </div>

            <div className="lg:flex-[0.15] xl:flex-[0.60]"></div>
            <div className="flex flex-1 flex-col gap-5 lg:flex-row lg:gap-[50px]">
              <div className="mb-5 flex max-w-[250px] min-w-28 flex-[1_0_0] flex-col gap-[10px]">
                <h6 className="text-[16px] font-bold text-[#FFFFFFB2] lg:text-[20px]">
                  {dictionary.footer.relatedRecommendations}
                </h6>

                <ul className="flex flex-col gap-[10px]">
                  {recommendations.map((recommendation, index) => (
                    <li key={index}>
                      <Link
                        url={`/service/${recommendation.url}`}
                        target={recommendation.target}
                        className="text-[16px] font-[400] text-inherit lg:text-[18px]"
                      >
                        {recommendation.children}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex min-w-20 flex-[1_0_0] flex-col gap-[10px] lg:min-w-30 xl:ml-10">
                <h6 className="text-[16px] font-bold text-[#FFFFFFB2] lg:text-[20px]">
                  {dictionary.footer.contactUs}
                </h6>

                <ul className="flex flex-col gap-[10px] text-[16px] font-[400] text-white lg:mb-5 lg:text-[18px]">
                  <li>
                    {dictionary.footer.email.title}:{' '}
                    <a
                      className="underline"
                      href="mailto:<EMAIL>"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <EMAIL>
                    </a>
                  </li>
                  <li>
                    {dictionary.footer.feedback.title}:{' '}
                    <a
                      className="underline"
                      href="https://t.me/hgc007"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      https://t.me/hgc007
                    </a>
                  </li>
                </ul>
                <div className="hidden auto-rows-[24px] grid-cols-[50px_50px_50px_50px_50px] items-center gap-4 lg:grid">
                  {medias.map((media, index) => (
                    <Link
                      key={index}
                      url={media.url}
                      target={media.target}
                      className="flex items-center"
                    >
                      {media.icon}{' '}
                      {index !== medias.length - 1 && (
                        <span className="ml-4 text-[#FF5542]">●</span>
                      )}
                    </Link>
                  ))}
                </div>
              </div>

              <div className="grid auto-rows-[24px] grid-cols-[50px_50px_50px_50px_50px] items-center gap-4 lg:hidden">
                {medias.map((media, index) => (
                  <Link
                    key={index}
                    url={media.url}
                    target={media.target}
                    className="flex items-center"
                  >
                    {media.icon}{' '}
                    {index !== medias.length - 1 && (
                      <span className="ml-4 text-[#FF5542]">●</span>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/** Second Row */}
          <div className="lg:text flex flex-col gap-6 text-[14px] leading-normal font-[400] text-white lg:flex-row lg:items-center lg:justify-between lg:text-center">
            <span>{dictionary.footer.copyright}</span>

            <div className="flex gap-6">
              {tnc.map((route, index) => (
                <Link key={index} url={route.url} target={route.target}>
                  {route.children}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
