'use client';

import DropDown from '@hi7/assets/icon/chevron-down-white.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import Link from '../Link';

const SubMenuLinkMobile = ({
  url,
  children,
  onClick,

  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const pathname = usePathname();
  const hasSubitem = items.length > 0;

  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div
        onClick={() => setIsOpen((prev) => !prev)}
        className={clsx(
          pathname === url || pathname.includes(url) ? 'text-hi7-primary' : '',
          'flex cursor-pointer items-center gap-2.5',
          'py-4 justify-end text-[24px] -mr-5',
        )}
      >
        {children}
        <svg className='w-3 h-3 font-black' fill='#04227D' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z' /></svg>
        <DropDown />
      </div>

      {hasSubitem && (
        <>
          {isOpen && (
            <div className='-mt-3 -mb-2 pr-5 text-end items-end justify-items-end text-[18px] font-[400]'>
              {items.map(({ url, text, icon }) => (
                <Link
                  key={url}
                  url={url}
                  onClick={onClick}
                  className={clsx(
                    'flex items-center gap-2 whitespace-nowrap',
                    'cursor-pointer',
                    'py-4',
                  )}
                >
                  {text}
                  <span className="text-[#FF5542]">{icon}</span>
                </Link>
              ))}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default SubMenuLinkMobile;
