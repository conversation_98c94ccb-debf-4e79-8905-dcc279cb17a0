'use client';

import Logo from '@hi7/assets/logo/logo.svg';
import LocaleSwitcher from '@hi7/components/LocaleSwitcher';
import MenuLink from '@hi7/components/MenuLink';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import Link from '../Link';
import P1 from './icons/p1.svg';
import P2 from './icons/p2.svg';
import P3 from './icons/p3.svg';
import P4 from './icons/p4.svg';

// type HeaderProps = Pick<i18n, 'dictionary'>;
// { dictionary }: HeaderProps
const Desktop = ({ dictionary, locale }: DictionaryProps) => {
  const ROUTES: MenuLinkProps[] = [
    {
      url: '/features',
      children: dictionary.header.features.title,
      items: [
        {
          icon: <P1 />,
          url: '/products-solutions/whatsapp-marketing-management',
          text: dictionary.header.productSolution.whatsAppMarketingManagement,
        },
        {
          icon: <P2 />,
          url: '/products-solutions/social-media-platform',
          text: dictionary.header.productSolution.socialMediaPlatforms,
        },
        {
          icon: <P3 />,
          url: '/features/global-data-acquisition/',
          text: dictionary.header.features.globalMarketingServices,
        },
        {
          icon: <P4 />,
          url: '/features/global-marketing-services',
          text: dictionary.header.features.globalDataAcquisition,
        },
      ],
    },
    {
      url: '/industry-insights',
      children: dictionary.header.industryInsights,
    },
    {
      url: 'https://mall.007tg.com/',
      children: dictionary.header.purchase,
    },
    {
      asButton: true,
      url: 'https://admin.scrmchampion.com/',
      children: `${dictionary.header.button.signUp}/${dictionary.header.button.logIn}`,
    },
  ];

  return (
    <>
      {/* <div className="h-[78px]"></div> */}
      <nav
        className={clsx(
          'z-30',
          'fixed top-7 left-1/2 -translate-x-1/2',
          'px-4',
          'w-[96%] min-w-[96%]',
          'flex items-center justify-between',
          'transform transition-all duration-200',
          'lg:h-[49px] lg:px-5 lg:py-4',
          'bg-white',
          'rounded-full shadow-lg',
        )}
      >
        <div className={clsx('flex')}>
          <Link url={''} className={clsx('text-[#047AFF]')}>
            <Logo className={'text-[#047AFF]'} />
          </Link>
        </div>

        <div className="flex items-center gap-10 text-[16px] font-medium text-[#04227D] xl:text-[18px]">
          {/* {ROUTES.map((route, index) => (
            <MenuLink key={index} {...route} />
          ))} */}


          {ROUTES.map((route, index) => {
            if (index === 0) {
              return (
                <MenuLink key={index} {...route}>
                  <span
                    className='hover:text-[#047AFF]'
                    dangerouslySetInnerHTML={{
                      __html: dictionary.header.features.title,
                    }}
                  />
                  <svg className='w-3 h-3 font-black' fill='#409eff' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z' /></svg>
                </MenuLink>
              )
            } else {
              return (
                <MenuLink key={index} {...route} />
              )
            }
          })}
          <div className="z-50">
            <LocaleSwitcher />
          </div>
        </div>
      </nav>
    </>
  );
};

export default Desktop;
