'use client';

import Link from '@hi7/components/Link';
import type { MenuLinkProps } from '@hi7/interface/link';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import SubMenuLink from '../SubMenuLink';

const MenuLink = (props: MenuLinkProps) => {
  const { url, asButton: button, onClick, children, items = [] } = props;
  const pathname = usePathname();

  const hasSubitem = items.length > 0;
  if (hasSubitem) {
    return <SubMenuLink {...props} />;
  }

  return (
    <Link
      onClick={onClick}
      url={url}
      className={clsx(
        button
          ? clsx(
            'w-max px-6 py-3',
            'rounded-3xl whitespace-nowrap',
            'cursor-pointer text-[#FF5542] hover:bg-[#C7E5FF]',
          )
          : '',
        pathname === url || pathname.includes(url) ? 'text-hi7-primary' : '',
        'hover:text-[#047AFF] relative flex cursor-pointer items-center gap-2.5',
      )}
    >
      {children}
    </Link>
  );
};

export default MenuLink;
