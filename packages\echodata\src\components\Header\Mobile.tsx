'use client';

import clsx from 'clsx';
import { useState } from 'react';

import CloseButton from '@hi7/assets/icon/close-button.svg';
import Hamburger from '@hi7/assets/icon/menu.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import Link from '@hi7/components/Link';
import LocaleSwitcher, {
  LocaleSwitcherButton,
} from '@hi7/components/LocaleSwitcher/Mobile';
import MenuLink from '@hi7/components/MenuLink/Mobile';

import type { DictionaryProps } from '@hi7/interface/i18n';
import type { MenuLinkProps } from '@hi7/interface/link';
import P1 from './icons/p1.svg';
import P2 from './icons/p2.svg';
import P3 from './icons/p3.svg';
import P4 from './icons/p4.svg';

// type HeaderProps = Pick<i18n, 'dictionary'>;
// { dictionary }: HeaderProps
const HeaderMobile = ({ dictionary }: DictionaryProps) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [localeOpen, setLocaleOpen] = useState(false);

  const toggleMenu = () => {
    setLocaleOpen(false);
    setMenuOpen((prev) => !prev);
  }
  const toggleLocale = () => {
    setMenuOpen(false);
    setLocaleOpen((prev) => !prev);
  }

  const open = menuOpen || localeOpen;

  const closePanel = () => {
    setLocaleOpen(false);
    setMenuOpen(false);
  };

  const ROUTES: MenuLinkProps[] = [
    {
      url: '/features',
      children: dictionary.header.features.title,
      items: [
        {
          icon: <P1 />,
          url: '/products-solutions/whatsapp-marketing-management',
          text: dictionary.header.productSolution.whatsAppMarketingManagement,
        },
        {
          icon: <P2 />,
          url: '/products-solutions/social-media-platform',
          text: dictionary.header.productSolution.socialMediaPlatforms,
        },
        {
          icon: <P3 />,
          url: '/features/global-data-acquisition/',
          text: dictionary.header.features.globalMarketingServices,
        },
        {
          icon: <P4 />,
          url: '/features/global-marketing-services',
          text: dictionary.header.features.globalDataAcquisition,
        },
      ],
    },
    {
      url: '/industry-insights',
      children: dictionary.header.industryInsights,
    },
    {
      url: 'https://mall.007tg.com/',
      children: dictionary.header.purchase,
    },
    {
      asButton: true,
      url: 'https://admin.scrmchampion.com/',
      children: `${dictionary.header.button.signUp}/${dictionary.header.button.logIn}`,
    },
  ];

  return (
    <>
      <nav
        className={clsx(
          'z-50',
          'fixed top-7 left-1/2 -translate-x-1/2',
          'px-4',
          'w-[96%] min-w-[96%]',
          'flex items-center justify-between',
          'bg-white',
          'rounded-full shadow-lg',
          'transition-all duration-200',
        )}
      >
        <Link url={''} onClick={closePanel}>
          <Logo className="text-[#409eff]" width="100%" height={36} />
        </Link>

        <div className="flex-1" />

        {!open && <LocaleSwitcherButton onClick={toggleLocale} />}
        {!open && <Hamburger className="text-[#047AFF]" onClick={toggleMenu} />}

        {open && <CloseButton onClick={closePanel} />}
      </nav>

      {/* Fullscreen overlay when menu is open */}
      {menuOpen && (
        <div className="fixed top-0 left-0 w-full h-screen bg-white z-80 p-6 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center">
            <Link url={''} onClick={closePanel}>
              <Logo className="text-[#409eff]" width="100%" height={36} />
            </Link>
            <LocaleSwitcherButton onClick={toggleLocale} />
            <Hamburger className="text-[#047AFF]" onClick={toggleMenu} />
          </div>
          <div className="flex flex-col gap-4 mt-6 text-[#047AFF]">
            {ROUTES.map((route, index) => (
              <MenuLink
                key={index}
                {...route}
                onClick={() => {
                  toggleMenu();
                }}
              />
            ))}
          </div>
        </div>
      )}

      {/* Locale switcher fullscreen overlay if open */}
      {localeOpen && (
        <div className="fixed top-0 left-0 w-full h-screen bg-white z-80 p-6 flex flex-col flex-col overflow-hidden">
          <div className="flex text-[18px] justify-between">
            <Link url={''} onClick={closePanel}>
              <Logo className="text-[#409eff]" width="100%" height={36} />
            </Link>
            <LocaleSwitcherButton onClick={toggleLocale} />
            <Hamburger className="text-[#047AFF] pt-1" onClick={toggleMenu} />
          </div>
          <div className="mt-6 justify-items-end">
            <LocaleSwitcher />
          </div>
        </div>
      )}
    </>
  );
};

export default HeaderMobile;
