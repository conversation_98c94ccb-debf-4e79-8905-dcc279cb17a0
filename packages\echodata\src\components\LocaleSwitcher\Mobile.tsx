'use client';

import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';

import { i18n, type Locale } from '@hi7/lib/i18n';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import { useParams } from 'next/navigation';
import { LANGUAGE_NAME } from './config';

export function LocaleSwitcherButton({ onClick }: { onClick: () => void }) {
  const params = useParams();
  const currentLocale = params.locale;

  return (
    <div
      className={clsx('relative flex cursor-pointer items-center gap-2.5 text-[#409eff]')}
      onClick={onClick}
    >
      {
        currentLocale === 'en'
          ?
          <div className="flex flex-row justify-center items-center font-bold mr-2 text-[18px]">
            <span className="">EN</span>
            <svg className="w-3 h-3 font-black ml-1" fill='#409eff' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z' /></svg>
          </div>
          :
          <div className="flex flex-row justify-center items-center font-bold mr-2 text-[18px]">
            <span className="">中</span>
            <svg className="w-3 h-3 font-black ml-1" fill='#409eff' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z' /></svg>
          </div>
      }
    </div>
  );
}

export default function LocaleSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);

  const [, currentEndPoint] = pathname.split('/').slice(1) as [Locale, string];

  const handleLocaleChange = (locale: Locale) => {
    setBaseurl(`/${locale}`);
    router.push(`/${locale}${currentEndPoint ? `/${currentEndPoint}` : ''}`);
  };

  return i18n.locales.map((locale) => (
    <button
      key={locale}
      onClick={() => handleLocaleChange(locale)}
      className={clsx('flex bg-transparent text-[#047AFF] text-[24px] font-[500] cursor-pointer items-center gap-2.5 py-4')}
    >
      {LANGUAGE_NAME[locale] ?? locale}
    </button>
  ));
}
