'use client';

import Rectangle1 from '@hi7/assets/background/rectangle-mobile1.svg?url';
import Rectangle2 from '@hi7/assets/background/rectangle-mobile2.svg?url';
import UnionMobile from '@hi7/assets/background/union-feature1-mobile.svg?url';
import Union from '@hi7/assets/background/union-feature1.svg?url';
import Whatsapp from '@hi7/assets/background/whatsapp.svg?url';

import type { DictionaryProps } from '@hi7/interface/i18n';
import { motion } from 'motion/react';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

interface HeroProps extends DictionaryProps {
  isActive?: boolean;
}

function Hero({ dictionary, isActive = true }: HeroProps) {
  return (
    <>
      <div className="relative z-0 flex items-center justify-center overflow-visible lg:z-auto lg:overflow-hidden">
        <div className="w-full">
          <div className="h-screen">
            {/******* Mobile *******/}
            <div className="relative block h-[72dvh] lg:hidden">
              <div className="relative h-full w-full transition-all duration-700 ease-in-out">
                <Image
                  fill
                  src={UnionMobile}
                  alt={
                    dictionary.productSolution.whatsAppMarketingManagement
                      .landing.title
                  }
                  className="object-cover"
                  priority
                />
              </div>

              {/******* Text Overlay *******/}
              <div className="absolute top-12 z-10 w-full px-6 text-start text-white">
                <h1
                  className={`mb-3 text-[42px] leading-[43px] font-semibold drop-shadow-md ${arsenal.className}`}
                >
                  {
                    dictionary.productSolution.whatsAppMarketingManagement
                      .landing.title
                  }
                </h1>
                <p className="text-md mb-6 font-thin">
                  {
                    dictionary.productSolution.whatsAppMarketingManagement
                      .landing.desc
                  }
                </p>
                <hr className="mb-6" />
                <div className="flex flex-col gap-5">
                  <a
                    href="#"
                    target="_blank"
                    className="flex w-[180px] cursor-pointer justify-center rounded-4xl bg-[#FF5542] py-3 text-[17px] font-semibold text-white transition duration-200 hover:bg-white hover:text-[#FF5542]"
                  >
                    {dictionary.general.freeTrial.button1}
                  </a>
                  <a
                    href="#"
                    target="_blank"
                    className="flex w-[180px] cursor-pointer justify-center rounded-4xl bg-[#047AFF] py-3 text-[17px] font-semibold text-white transition duration-200 hover:bg-white hover:text-[#FF5542]"
                  >
                    {dictionary.general.freeTrial.button5}
                  </a>
                </div>
              </div>
            </div>

            {/****** Desktop ******/}
            <div className="relative hidden w-full items-center justify-center lg:flex lg:h-[60%]">
              <Image
                width={1920}
                height={1080}
                src={Union}
                alt={
                  dictionary.productSolution.whatsAppMarketingManagement.landing
                    .title
                }
                className="object-cover"
              />
              <div className="absolute top-30 left-21 z-10 text-start whitespace-break-spaces text-white 2xl:top-40 2xl:left-25">
                <h1
                  className={`mb-6 text-[52px] leading-[55px] font-bold tracking-[1px] 2xl:text-[83px] 2xl:leading-[85px] ${arsenal.className}`}
                >
                  {
                    dictionary.productSolution.whatsAppMarketingManagement
                      .landing.title
                  }
                </h1>
                <p className="mb-5 text-[21px] leading-[30px] font-thin text-[#e9f3ff] 2xl:mb-10 2xl:text-[33px] 2xl:leading-[40px]">
                  {
                    dictionary.productSolution.whatsAppMarketingManagement
                      .landing.desc
                  }
                </p>
                <hr className="mb-6 w-[570px] 2xl:mb-10 2xl:w-[900px]" />
                <div className="flex gap-4">
                  <a
                    href="#"
                    target="_blank"
                    className="flex w-[12dvw] cursor-pointer justify-center rounded-4xl bg-[#FF5542] py-2 text-[13px] font-semibold text-white transition duration-200 hover:bg-white hover:text-[#FF5542] 2xl:w-[200px] 2xl:py-3 2xl:text-lg"
                  >
                    {dictionary.general.freeTrial.button1}
                  </a>
                  <a
                    href="#"
                    target="_blank"
                    className="flex w-[12dvw] cursor-pointer justify-center rounded-4xl bg-[#047AFF] py-2 text-[13px] font-semibold text-white transition duration-200 hover:bg-white hover:text-[#FF5542] 2xl:w-[200px] 2xl:py-3 2xl:text-lg"
                  >
                    {dictionary.general.freeTrial.button5}
                  </a>
                </div>
              </div>
            </div>
            <div className="absolute right-0 bottom-0 h-[40dvh] w-[45dvw] md:h-[52dvh] md:w-[33dvw]">
              <Image
                src={Whatsapp}
                alt="whatsapp"
                className="w-[28dvw] object-contain md:w-[11dvw]"
              />
              <Image
                src={Rectangle1}
                alt="rec1"
                className="absolute right-[-3dvw] bottom-18 block object-contain md:hidden"
              />
              <Image
                src={Rectangle2}
                alt="rec2"
                className="absolute top-0 right-0 block object-contain md:hidden md:w-[11dvw]"
              />
            </div>
            <motion.div
              className="shape1 absolute bottom-[15dvh] left-[50vw] h-[20dvw] w-[20dvw] rounded-full bg-[#04227D] md:bottom-[-15vw] md:left-[60vw] md:h-[26vw] md:w-[26vw]"
              initial={{ x: 0, y: 0, opacity: 1, scale: 1 }}
              animate={
                !isActive
                  ? { x: '-30vw', y: '-70vh', opacity: 1, scale: 1 }
                  : { x: 0, y: 0, opacity: 1, scale: 1 }
              }
              transition={{ duration: 0.5, ease: 'linear' }}
            />

            <motion.div
              className="shape2 absolute z-2 hidden w-[30vw] rotate-[45deg] rounded-full bg-[#04227D] md:right-[-10vw] md:bottom-[8vh] md:h-[33vh] lg:block"
              initial={{ x: 0, y: 0, opacity: 1, scale: 1 }}
              animate={
                !isActive
                  ? { x: '-50vw', y: 0, opacity: 1, scale: 1 }
                  : { x: 0, y: 0, opacity: 1, scale: 1 }
              }
              transition={{ duration: 0.5, ease: 'linear' }}
            />
          </div>
        </div>
      </div>
    </>
  );
}

export default Hero;
