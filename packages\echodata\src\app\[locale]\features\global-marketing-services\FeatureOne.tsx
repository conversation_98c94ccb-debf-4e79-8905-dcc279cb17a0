import SlidingPolygon from '@hi7/assets/background/global-market-vector-bg.png';
import MeetingImg from '@hi7/assets/background/meeting-background.jpg';
import Point1 from '@hi7/assets/icon/api-screen.svg';
import Point2 from '@hi7/assets/icon/feature8.svg';
import AnimationFrame from '@hi7/components/AnimationFrame';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function AppIntergration({ dictionary }: DictionaryProps) {

  const points = [
    {
      icon: Point1,
      title: dictionary.features.globalMarketingServices.section1.apiInterface.title,
      desc: dictionary.features.globalMarketingServices.section1.apiInterface.desc,
    },
    {
      icon: Point2,
      title: dictionary.features.globalMarketingServices.section1.filteringAndMarketing.title,
      desc: dictionary.features.globalMarketingServices.section1.filteringAndMarketing.desc,
    },
  ];

  return (
    <section className="h-auto relative -mt-30 md:mt-40 lg:mt-10 lg:mb-10">
      <div className="lg:hidden">
        <div className="flex flex-row md:flex-row h-auto lg:h-[130px]">
          <div
            className={clsx(
              'w-full bg-[#E9F3FF] rounded-t-[40px] items-end flex justify-end pr-6 -mb-5 z-10 order-2',
              'md:-mb-10 md:order-1 md:w-1/3 md:pr-10 md:mb-0 ',
              'lg:w-4/9 lg:rounded-t-[70px] lg:items-end lg:justify-start lg:pr-0 lg:pl-16 lg:-mb-27',
              'xl:pl-20'
            )}
          >
            <h2
              className={`${arsenal.className} text-right font-bold text-[40px] text-[#FF5542] leading-[36px] md:text-[45px] lg:text-[64px] lg:text-left lg:leading-[72px]`}
              dangerouslySetInnerHTML={{
                __html: dictionary.features.globalMarketingServices.section1.title,
              }}
            />
          </div>

          <div className="relative order-1 md:order-2 w-full md:w-2/3 lg:w-5/9">
            <Image
              src={MeetingImg}
              alt={'insight-img'}
              className={clsx(
                '-mt-28 border-r-25 border-b-25 border-t-25 rounded-r-[45px] border-white object-cover h-[30vh] w-full ',
                'md:mr-[140px] md:border-l-25 md:border-r-0 md:rounded-[70px] md:rounded-r-none',
                'lg:h-[50vh] lg:translate-y-[-5%] lg:-ml-0 lg:border-r-[45px] lg:rounded-[85px] lg:mx-50 lg:border-l-35 lg:border-b-30',
                'xl:h-[45vh] xl:translate-y-[-20%]',
              )}
            />
          </div>
        </div>

        <div className="bg-[#E9F3FF] rounded-br-[40px] pt-20 pb-10 -mt-[50px] lg:rounded-tr-[150px] lg:rounded-b-[60px] lg:mt-0 lg:pt-30">
          <hr className="border-[#FF5542] w-[90%] mx-auto xl:w-[92%] xl:mt-8" />
          <div className="grid grid-cols-1 gap-0 md:grid-cols-2 lg:grid-cols-3 mt-6 lg:mt-0 lg:px-6 lg:pb-6 xl:gap-13 xl:mt-5">
            {points.length > 0 &&
              points.map((point, index) => (
                <div
                  key={index}
                  className="flex flex-col gap-4 rounded-[30px] bg-[#E9F3FF] p-7 w-full mb-0 lg:px-10 lg:py-8 xl:px-20"
                >
                  <div className="flex flex-row items-center justify-start xl:justify-start text-[#FF5542]">
                    <point.icon className="w-[28%] xl:w-[20%]" />
                    <h1 className="text-[24px] font-[500] pl-3 lg:text-[24px] leading-[25px] xl:text-[29px]">
                      {point.title}
                    </h1>
                  </div>
                  <hr />
                  <span className="text-[18px] font-[300] text-[#04227D] xl:text-[25px]">
                    {point.desc}
                  </span>
                </div>
              ))}
          </div>
        </div>
      </div>

      <div className="hidden lg:flex lg:flex-row items-center justify-center">
        <AnimationFrame variant="SlideToRight" once={false} className="w-full relative">
          <div className="w-5/7 translate-x-[-8%] content-center">
            <Image
              src={MeetingImg}
              alt={'insight-img'}
              className={clsx(
                'rounded-[50px] h-[80vh] w-full object-cover',
              )}
            />
          </div>
          <div className="relative w-full h-full mt-10">
            <Image
              src={SlidingPolygon}
              alt="background"
              className="object-cover w-full h-full scale-y-115 xl:scale-y-100"
            />

            <AnimationFrame variant="FadeIn" once={false} className="absolute top-7 left-0 px-8 lg:px-16 w-full max-w-[700px] xl:left-15 xl:top-30">
              <div className="">
                <h1
                  className={`${arsenal.className} text-right font-bold text-[40px] text-[#FF5542] leading-[36px] md:text-[45px] lg:text-[64px] lg:text-left lg:leading-[65px]`}
                  dangerouslySetInnerHTML={{
                    __html: dictionary.features.globalMarketingServices.section1.title,
                  }}
                />
                <hr className="border-[#FF5542] w-[55%] my-5 xl:w-[90%]" />
                <div className="flex flex-col w-full mt-8 mb-4 xl:mt-20">
                  {points.length > 0 &&
                    points.map((point, index) => (
                      <div className="mb-10 xl:mb-20" key={index}>
                        <div className="flex flex-row items-center justify-start text-[#FF5542]">
                          <point.icon className="w-[12%] xl:w-[20%]" />
                          <h1 className="text-[24px] font-[500] lg:text-[24px] leading-[25px] xl:text-[29px] w-[45%] xl:w-[60%] xl:leading-[32px]">
                            {point.title}
                          </h1>
                        </div>
                        <hr className="w-[55%] my-3 xl:w-[90%]" />
                        <p className="w-[55%] text-[#04227D] font-[400] xl:w-[90%] xl:text-[20px]">{point.desc}</p>
                      </div>
                    ))}
                </div>
              </div>
            </AnimationFrame>
          </div>
        </AnimationFrame>
      </div>
    </section >
  );
}

export default AppIntergration;
