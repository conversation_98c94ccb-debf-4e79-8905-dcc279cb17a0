# PageWrapper Component Enhancement

## Overview

The enhanced `PageWrapper` component now supports custom scroll directions for individual sections. This allows you to control whether a section slides up (default behavior) or slides down when transitioning to the next section.

## Features

### Core Features (Existing)
- ✅ Smooth scroll navigation between sections
- ✅ Mouse wheel and keyboard navigation support
- ✅ Touchpad vs mouse wheel detection with different cooldowns
- ✅ Tall section handling (scrolls within section before moving to next)
- ✅ Mobile-friendly with passive scroll tracking
- ✅ Active section tracking and state management

### New Enhancement
- ✅ **Custom scroll directions** - Control whether sections slide up or down during transitions
- ✅ **Overlay animation system** - Smooth visual transitions with CSS animations
- ✅ **TypeScript support** - Proper typing for scroll direction props

## Usage

### Basic Usage (Default Behavior)
```tsx
import PageWrapper from '@hi7/components/PageWrapper';

function MyPage() {
  return (
    <PageWrapper>
      <SectionOne />   {/* Normal: slides up when scrolling down */}
      <SectionTwo />   {/* Normal: slides up when scrolling down */}
      <SectionThree /> {/* Normal: slides up when scrolling down */}
    </PageWrapper>
  );
}
```

### Custom Scroll Direction
```tsx
import PageWrapper from '@hi7/components/PageWrapper';
import type { PageWrapperSectionProps } from '@hi7/interface/page-wrapper';

const SectionTwo = ({ isActive, isMobile, scrollDirection }: PageWrapperSectionProps) => (
  <div className="min-h-screen bg-green-500">
    <h1>Section with Custom Scroll Direction</h1>
    <p>This section will slide DOWN when transitioning</p>
  </div>
);

function MyPage() {
  return (
    <PageWrapper>
      <SectionOne />                           {/* Normal: slides up */}
      <SectionTwo scrollDirection="down" />    {/* Custom: slides down */}
      <SectionThree />                         {/* Normal: slides up */}
    </PageWrapper>
  );
}
```

## Props

### PageWrapper Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | React.ReactNode | required | Section components to wrap |

### Section Component Props (Injected by PageWrapper)
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| scrollDirection | 'up' \| 'down' \| 'default' | 'default' | Controls scroll animation direction |
| isActive | boolean | false | Whether this section is currently active |
| isMobile | boolean | false | Whether the device is mobile (<768px) |

## Scroll Direction Options

- **`'default'` or `undefined`**: Standard behavior - section slides up when scrolling down
- **`'down'`**: Custom behavior - section slides down when scrolling down  
- **`'up'`**: Custom behavior - section slides up when scrolling up (for reverse animations)

## Animation Details

- **Duration**: 800ms for custom scroll animations
- **Easing**: `cubic-bezier(0.76, 0, 0.24, 1)` for smooth transitions
- **Overlay System**: Uses a fixed overlay to create seamless visual transitions
- **Mobile**: Custom animations are disabled on mobile devices for performance

## Navigation Controls

- **Mouse Wheel**: Scroll up/down to navigate between sections
- **Keyboard**: Arrow keys, Page Up/Down, Space, Home, End
- **Touchpad**: Intelligent detection with appropriate cooldown periods
- **Mobile**: Native scroll behavior with section tracking

## Technical Implementation

The enhancement works by:

1. **Detection**: Checking if the current section has a `scrollDirection` prop
2. **Overlay Creation**: Cloning the current section into a fixed overlay
3. **Instant Scroll**: Moving the page to the target position (hidden under overlay)
4. **Animation**: Sliding the overlay in the specified direction
5. **Cleanup**: Removing the overlay after animation completes

## Performance Considerations

- Animations are disabled on mobile devices (< 1024px width)
- Uses `requestAnimationFrame` for smooth animations
- Proper cleanup of DOM elements and event listeners
- Cooldown periods prevent excessive scroll events

## Browser Support

- Modern browsers with CSS transforms and transitions
- Fallback to standard scroll behavior if animations fail
- Mobile-optimized with passive event listeners
