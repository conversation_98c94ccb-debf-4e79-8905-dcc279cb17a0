'use client';

import ContactUsButton from '@hi7/assets/icon/contact-us-button.svg';
import ContactUsClose from '@hi7/assets/icon/contact-us-close.svg';
import ContactUsText from '@hi7/assets/icon/contact-us-text.svg';
import ContactUsTG from '@hi7/assets/icon/contact-us-tg.svg';
import ContactUsWS from '@hi7/assets/icon/contact-us-ws.png';
import LatestNews from '@hi7/assets/icon/latest-news.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { useRef, useState } from 'react';
import Link from '../Link';

function Fab({ dictionary }: DictionaryProps) {
  const [isLatestOpen, setIsLatestOpen] = useState(false);
  const clearLatestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleLatestMouseEnter() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    setIsLatestOpen(true);
  }

  function handleLatestMouseLeave() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    clearLatestTimeoutRef.current = setTimeout(() => {
      setIsLatestOpen(false);
    }, 500);
  }

  const [isContactOpen, setIsContactOpen] = useState(false);
  const clearContactTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  function handleContactMouseEnter() {
    clearContactTimeoutRef.current &&
      clearTimeout(clearContactTimeoutRef.current);
    setIsContactOpen(true);
  }

  function handleContactMouseLeave() {
    clearContactTimeoutRef.current &&
      clearTimeout(clearContactTimeoutRef.current);
    clearContactTimeoutRef.current = setTimeout(() => {
      setIsContactOpen(false);
    }, 800);
  }

  return (
    <>
      <div
        className="fixed right-0 bottom-[20%] z-50 lg:right-[5%]"
        onMouseEnter={handleContactMouseEnter}
        onMouseLeave={handleContactMouseLeave}
      >
        {isContactOpen && (
          <div className="animate-fab-latest-contact absolute right-0 -bottom-[47px] lg:-right-8">
            <div className="mb-[-30px] ml-[10px]">
              <ContactUsText />
            </div>
            <a
              className="mb-[-45px] block"
              href="https://007tg.com/ccs/champions"
              target="_blank"
              rel="noopener noreferrer"
            >
              <ContactUsTG />
            </a>

            <a
              className="relative mb-[-45px] block h-[110px] w-[110px]"
              href={dictionary.footer.customerService.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image src={ContactUsWS} alt="Contact Us WhatsApp" fill />
            </a>
            <ContactUsClose />
          </div>
        )}

        <div
          className={clsx(
            'absolute',
            isContactOpen && 'animate-fab-latest-contact-reserve',
          )}
        >
          {!isContactOpen && (
            <div className="absolute right-4 bottom-4 z-50 lg:-right-4">
              <ContactUsText />
            </div>
          )}

          <div className="absolute right-8 -bottom-7 z-50 lg:right-0">
            <ContactUsButton className="scale-110" />
          </div>
        </div>
      </div>

      <Link
        url=""
        className={clsx(
          'fixed bottom-[10%] right-8 z-50 h-[48px] overflow-hidden rounded-full lg:bottom-[5%] lg:right-[5%]',
          isLatestOpen ? 'w-[165px]' : 'w-[48px]',
        )}
        onMouseEnter={handleLatestMouseEnter}
        onMouseLeave={handleLatestMouseLeave}
      >
        {/* The LatestNews icon container will now be absolutely positioned to the right */}
        <div className="absolute right-0 top-0 z-50 flex h-[48px] w-[48px] scale-110 items-center justify-center rounded-full bg-[#04227D]">
          <LatestNews />
        </div>

        {/* The Latest News text will be absolutely positioned to the left */}
        {isLatestOpen && (
          <div className="animate-fab-latest-news absolute top-[1px] left-0 h-[48px] w-[165px] rounded-[50px] bg-[#047AFF] pl-4 pr-[50px] text-left text-[14px] leading-[48px] font-bold text-white">
            Latest News
          </div>
        )}
      </Link>
      <Link
        url=""
        className={clsx(
          'fixed bottom-[3%] right-7 z-50 h-[50px] w-[50px] overflow-hidden rounded-full lg:left-[65px] md:hidden',
        )}
      >
        <div className="text-white relative flex h-[48px] w-[48px] items-center justify-center text-[16px] leading-[17px] text-center rounded-full bg-[#FF5542]">
          Try<br />Now
        </div>
      </Link>

    </>
  );
}

export default Fab;
